<template>
  <div class="pr-70px flex flex-col gap-2 relative">
    <div v-for="(feature, index) in modelValue" :key="feature.name + index" class="flex gap-2 items-center">
      <el-input v-model="feature.name" placeholder="名称" class="flex-1" />
      <el-input v-model="feature.value" placeholder="值" class="flex-1" />
      <div class="flex w-60px right-0 absolute">
        <el-button type="danger" circle size="small" @click="handleDeleteFeature(index)">
          <el-icon><Delete /></el-icon>
        </el-button>
        <el-button v-if="index === modelValue.length - 1" type="primary" circle size="small" @click="handleAddFeature">
          <el-icon><Plus /></el-icon>
        </el-button>
      </div>
    </div>
    <el-button v-if="modelValue.length === 0" type="primary" circle size="small" @click="handleAddFeature">
      <el-icon><Plus /></el-icon>
    </el-button>
  </div>
</template>

<script setup lang="ts">
  import { Delete, Plus } from '@element-plus/icons-vue';

  interface Feature {
    name: string;
    value: string;
  }

  const modelValue = defineModel<Feature[]>({ required: true });

  const handleAddFeature = () => {
    modelValue.value = [...modelValue.value, { name: '', value: '' }];
  };

  const handleDeleteFeature = (index: number) => {
    const newFeatures = [...modelValue.value];
    newFeatures.splice(index, 1);
    modelValue.value = newFeatures;
  };
</script>
