/*
 * @OriginalName: 角色的权限分配模块
 * @Description: 分配和回收系统中角色的权限
 */
import { request } from '@/utils/request';

/**
 * 赋权或更新赋权
 * @description 根据角色记录主键ID和权限ID，给角色赋予或更新多个权限。
 */
export function assignPrivilege2(data: RolePrivilegeDTO2) {
  return request<RListRolePrivilegeVO>(`/rolePrivilege/assignPrivilege2`, {
    method: 'post',
    data,
  });
}

/**
 * 赋权或更新赋权
 * @description 根据角色记录主键ID和权限ID，给角色赋予或更新一个权限。
 */
export function assignPrivilege1(data: RolePrivilegeDTO) {
  return request<RRolePrivilegeVO>(`/rolePrivilege/assignPrivilege1`, {
    method: 'post',
    data,
  });
}

/**
 * 查看角色的赋权信息
 * @description 根据角色ID，查看给角色赋予权限的全部信息。
 */
export function findRolePrivilegeByRoleIdVO(roleId: number, params?: { roleId: number }) {
  return request<RListRolePrivilegeVO>(`/rolePrivilege/findRolePrivilegeByRoleId/${roleId}`, {
    method: 'get',
    params,
  });
}

/**
 * 解除赋予角色的权限
 * @description 根据角色记录主键ID和权限ID解除赋予角色赋予权限。
 */
export function deleteRolePrivilege(roleId: number, params?: { roleId: number; privilegeIds: Array<number> }) {
  return request<R>(`/rolePrivilege/deleteRolePrivilege/${roleId}`, {
    method: 'get',
    params,
  });
}
