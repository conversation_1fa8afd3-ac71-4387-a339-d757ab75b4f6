/*
 * @Description: 用户模块
 */
import request from './request';
import { encryption } from '@/utils/crypto';
// import { updatePassword } from './index';

/**
 * https://www.ietf.org/rfc/rfc6749.txt
 * OAuth 协议 4.3.1 要求格式为 form 而不是 JSON 注意！
 */
export const FORM_CONTENT_TYPE = 'application/x-www-form-urlencoded';

interface LoginData {
  username: string;
  password: string;
  randomStr: string;
  code: string;
}
/**
 * 登录
 */
export const postLogin = (data: LoginData) => {
  const basicAuth = 'Basic ' + window.btoa(import.meta.env.VITE_OAUTH2_PASSWORD_CLIENT);
  window.sessionStorage.setItem('basicAuth', JSON.stringify(basicAuth));
  let encPassword = data.password;
  // 密码加密
  if (import.meta.env.VITE_PWD_ENC_KEY) {
    encPassword = encryption(data.password, import.meta.env.VITE_PWD_ENC_KEY);
  }
  const { username, randomStr, code } = data;
  return request({
    url: '/auth/oauth2/token',
    method: 'post',
    // params: { username, randomStr, code, grant_type: 'password', scope: 'server' },
    // data: { password: encPassword },
    data: { username, grant_type: 'password', scope: 'server', password: encPassword },
    headers: {
      skipToken: true,
      Authorization: basicAuth,
      'Content-Type': FORM_CONTENT_TYPE,
    },
    baseURL: '/api',
  });
};

/**
 * 退出登录
 */
export const deleteLogout = () => {
  return request({
    url: '/auth/token/logout',
    method: 'delete',
    baseURL: '/api',
  });
};

/**
 * 修改密码
 */
export const changePwd = (data: any) => {
  // 密码加密
  data.password = encryption(data.password!, import.meta.env.VITE_PWD_ENC_KEY);
  data.oldPassword = encryption(data.oldPassword!, import.meta.env.VITE_PWD_ENC_KEY);
  // return updatePassword(data);
};
