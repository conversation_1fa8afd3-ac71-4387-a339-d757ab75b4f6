import { defineConfig, presetIcons, presetWind3 } from 'unocss';

export default defineConfig({
  presets: [
    presetWind3(),
    presetIcons({
      scale: 1.2,
      warn: true,
      collections: {
        mdi: () => import('@iconify-json/mdi/icons.json').then((i) => i.default),
        uiw: () => import('@iconify-json/uiw/icons.json').then((i) => i.default),
      },
    }),
  ],
  theme: {
    colors: {
      p: '#007f99',
      m: '#303333',
      tip: '#909399',
      baf: '#f7f9fc',
      bac: '#f0f2f5',
    },
  },
});
