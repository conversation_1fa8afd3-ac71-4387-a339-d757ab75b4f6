---
description: 
globs: 
alwaysApply: false
---
1.用pnpm装包
2.使用vue3,ts,steup语法
3.组件用element-plus,但是不要使用Layout布局相关组件
4.css尽量用unocss,重复程度比较高的css用scss
6.vue和vue-router用了自动引入,不需要再单独引用vue和vue-router中的东西,例如 import { ref, nextTick } from 'vue';像ref, nextTick是不需要手动引入的
5.1 封装组件时,defineProps使用类型单独声明例如interface Props {msg?: string}、默认值使用解构赋值例如const { msg = 'hello'} = defineProps<Props>()
5.2 封装组件时,defineEmits使用3.3+更简洁的语法例如const emit = defineEmits<{change: [id: number]}>()
5.3 封装组件时,假如父组件使用v-model,则子组件用用例如const modelValue = defineModel<string>({ required: true })语法去接收父组件的值,而不是使用defineEmits update:modelValue

7.当前vue版本是3.5.13,尽量使用当前版本能用的最新的vue语法