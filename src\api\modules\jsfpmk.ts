/*
 * @OriginalName: 角色分配模块
 * @Description: 分配和调整系统用户的角色
 */
import { request } from '@/utils/request';

/**
 * 分配或更新用户承担的系统用户角色
 * @description 根据用户的主键ID和角色ID，给用户分配系统用户分配角色或跟新角色分配信息。
 */
export function assignRole2(data: UserRoleDTO2) {
  return request<RListUserRoleVO>(`/userRole/assignRole2`, {
    method: 'post',
    data,
  });
}

/**
 * 查看用户承担的系统用户角色
 * @description 根据用户ID，查看用户承担的全部系统用户角色。
 */
export function findUserRoleVOByUserIdVO(userId: number, params?: { userId: number }) {
  return request<RListUserRoleVO>(`/userRole/findUserRoleVOByUserIdVO/${userId}`, {
    method: 'get',
    params,
  });
}

/**
 * 解除用户承担的系统用户角色
 * @description 根据用户的主键ID和角色ID解除用户承担的系统用户角色。
 */
export function deleteUserRole(userId: number, params?: { userId: number; roledIds: Array<number> }) {
  return request<R>(`/userRole/deleteUserRole/${userId}`, {
    method: 'get',
    params,
  });
}
