{"extends": "@vue/tsconfig/tsconfig.dom.json", "compilerOptions": {"tsBuildInfoFile": "./node_modules/.tmp/tsconfig.app.tsbuildinfo", "baseUrl": ".", "paths": {"@/*": ["src/*"]}, "types": ["vite/client", "element-plus/global"], "strict": true, "noUnusedLocals": false, "noUnusedParameters": true, "noFallthroughCasesInSwitch": true, "noUncheckedSideEffectImports": true}, "include": ["src/**/*.ts", "src/**/*.tsx", "src/**/*.vue", "src/**/*.d.ts", "./auto-imports.d.ts", "./components.d.ts"]}