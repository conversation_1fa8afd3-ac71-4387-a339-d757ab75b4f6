<template>
  <div class="h-full">
    <div v-loading="loading" class="tool-manage">
      <!-- 页面头部 -->
      <div class="header flex items-center justify-between px-5 py-4">
        <div>
          <h2 class="text-2xl font-bold">工具管理</h2>
          <p class="mt-1 text-gray-500">管理系统中的所有工具，包括上架、下架、编辑等操作</p>
        </div>
        <div>
          <el-button type="primary" :icon="Plus" @click="handleCreate"> 添加工具 </el-button>
        </div>
      </div>

      <!-- 统计卡片 -->
      <div class="mb-4 px-5">
        <el-row :gutter="16">
          <el-col :span="6">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-content">
                <div class="stat-number text-blue-600">{{ statistics.total }}</div>
                <div class="stat-label">总工具数</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-content">
                <div class="stat-number text-green-600">{{ statistics.online }}</div>
                <div class="stat-label">已上架</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-content">
                <div class="stat-number text-red-600">{{ statistics.offline }}</div>
                <div class="stat-label">已下架</div>
              </div>
            </el-card>
          </el-col>
          <el-col :span="6">
            <el-card shadow="hover" class="stat-card">
              <div class="stat-content">
                <div class="stat-number text-orange-600">{{ statistics.maintenance }}</div>
                <div class="stat-label">维护中</div>
              </div>
            </el-card>
          </el-col>
        </el-row>
      </div>

      <!-- 筛选条件 -->
      <div class="flex gap-4 bg-gray-50 px-5 py-3">
        <div>
          <el-input v-model="filters.name" placeholder="请输入工具名称" clearable style="width: 200px">
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
        </div>
        <div>
          <el-select v-model="filters.category" placeholder="所有类别" clearable style="width: 150px">
            <el-option v-for="item in categoryOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div>
          <el-select v-model="filters.type" placeholder="所有类型" clearable style="width: 150px">
            <el-option v-for="item in typeOptions" :key="item.value" :label="item.label" :value="item.value" />
          </el-select>
        </div>
        <div>
          <el-select v-model="filters.status" placeholder="所有状态" clearable style="width: 150px">
            <el-option label="已上架" value="online" />
            <el-option label="已下架" value="offline" />
            <el-option label="维护中" value="maintenance" />
          </el-select>
        </div>
        <div>
          <el-button type="primary" :icon="Search" @click="fetchData">搜索</el-button>
          <el-button :icon="Refresh" @click="resetFilters">重置</el-button>
        </div>
      </div>

      <!-- 批量操作 -->
      <div class="px-5 py-2" v-if="selectedTools.length > 0">
        <el-alert :title="`已选择 ${selectedTools.length} 个工具`" type="info" show-icon :closable="false">
          <template #default>
            <div class="mt-2 flex gap-2">
              <el-button size="small" type="success" @click="batchOnline">批量上架</el-button>
              <el-button size="small" type="warning" @click="batchOffline">批量下架</el-button>
              <el-button size="small" type="info" @click="batchMaintenance">批量维护</el-button>
              <el-button size="small" type="danger" @click="batchDelete">批量删除</el-button>
            </div>
          </template>
        </el-alert>
      </div>

      <!-- 工具列表 -->
      <div class="h-0 flex flex-1 flex-col px-5 pb-2">
        <el-table
          ref="tableRef"
          class="c-table-header h-0 flex-1"
          :data="tableData"
          style="width: 100%"
          @selection-change="handleSelectionChange"
        >
          <el-table-column type="selection" width="55" />
          <el-table-column prop="name" label="工具名称" min-width="150">
            <template #default="{ row }">
              <div class="flex items-center gap-2">
                <el-avatar :size="32" :src="row.icon" shape="square">
                  <el-icon><Tools /></el-icon>
                </el-avatar>
                <div>
                  <div class="font-medium">{{ row.name }}</div>
                  <div class="text-xs text-gray-500">{{ row.version }}</div>
                </div>
              </div>
            </template>
          </el-table-column>
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="category" label="类别" width="120" />
          <el-table-column prop="type" label="类型" width="100" />
          <el-table-column prop="provider" label="提供者" width="120" />
          <el-table-column prop="status" label="状态" width="100">
            <template #default="{ row }">
              <el-tag :type="getStatusType(row.status)" size="small">
                {{ getStatusText(row.status) }}
              </el-tag>
            </template>
          </el-table-column>
          <el-table-column prop="updateTime" label="更新时间" width="160" />
          <el-table-column label="操作" width="200" fixed="right">
            <template #default="{ row }">
              <div class="flex gap-2">
                <el-button size="small" type="primary" @click="handleEdit(row)">编辑</el-button>
                <el-dropdown @command="(command) => handleStatusChange(row, command)">
                  <el-button size="small" type="info">
                    状态<el-icon class="el-icon--right"><arrow-down /></el-icon>
                  </el-button>
                  <template #dropdown>
                    <el-dropdown-menu>
                      <el-dropdown-item command="online" :disabled="row.status === 'online'"> 上架 </el-dropdown-item>
                      <el-dropdown-item command="offline" :disabled="row.status === 'offline'"> 下架 </el-dropdown-item>
                      <el-dropdown-item command="maintenance" :disabled="row.status === 'maintenance'">
                        维护
                      </el-dropdown-item>
                    </el-dropdown-menu>
                  </template>
                </el-dropdown>
                <el-button size="small" type="danger" @click="handleDelete(row)">删除</el-button>
              </div>
            </template>
          </el-table-column>
        </el-table>

        <!-- 分页 -->
        <div class="pagination-container mt-4 flex justify-center">
          <el-pagination
            background
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50, 100]"
            :total="total"
            layout="total, sizes, prev, pager, next, jumper"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>
    </div>

    <!-- 工具编辑对话框 -->
    <ToolEditDialog v-model="editDialogVisible" :tool="currentTool" @success="handleEditSuccess" />
  </div>
</template>

<script setup lang="ts">
  import { ref, reactive, onMounted, watch } from 'vue';
  import { ElMessage, ElMessageBox } from 'element-plus';
  import { Plus, Search, Refresh, Tools, ArrowDown } from '@element-plus/icons-vue';
  import type { Tool } from '@/api/mock/tools';
  import {
    getToolList,
    deleteTool,
    batchDeleteTools,
    onlineTool,
    offlineTool,
    maintenanceTool,
    batchUpdateToolStatus,
    getToolStatistics,
    getToolCategories,
    getToolTypes,
  } from '@/api/mock/tools';
  import ToolEditDialog from './components/ToolEditDialog.vue';

  // 响应式数据
  const loading = ref(false);
  const tableData = ref<Tool[]>([]);
  const selectedTools = ref<Tool[]>([]);
  const currentPage = ref(1);
  const pageSize = ref(20);
  const total = ref(0);
  const editDialogVisible = ref(false);
  const currentTool = ref<Tool | null>(null);

  // 统计数据
  const statistics = ref({
    total: 0,
    online: 0,
    offline: 0,
    maintenance: 0,
  });

  // 筛选条件
  const filters = reactive({
    name: '',
    category: '',
    type: '',
    status: '',
  });

  // 选项数据
  const categoryOptions = ref<{ label: string; value: string }[]>([]);
  const typeOptions = ref<{ label: string; value: string }[]>([]);

  // 计算属性
  const getStatusType = (status: string): 'success' | 'warning' | 'info' | 'primary' | 'danger' => {
    const typeMap: Record<string, 'success' | 'warning' | 'info' | 'primary' | 'danger'> = {
      online: 'success',
      offline: 'danger',
      maintenance: 'warning',
    };
    return typeMap[status] || 'info';
  };

  const getStatusText = (status: string) => {
    const textMap: Record<string, string> = {
      online: '已上架',
      offline: '已下架',
      maintenance: '维护中',
    };
    return textMap[status] || status;
  };

  // 方法
  const fetchData = async () => {
    try {
      loading.value = true;
      const params = {
        ...filters,
        pageNum: currentPage.value,
        pageSize: pageSize.value,
      };
      const response = await getToolList(params);
      if (response.code === 0) {
        tableData.value = response.data.content;
        total.value = response.data.totalElements;
      }
    } catch (error) {
      console.error('获取工具列表失败:', error);
      ElMessage.error('获取工具列表失败');
    } finally {
      loading.value = false;
    }
  };

  const fetchStatistics = async () => {
    try {
      const response = await getToolStatistics();
      if (response.code === 0) {
        statistics.value = response.data;
      }
    } catch (error) {
      console.error('获取统计数据失败:', error);
    }
  };

  const fetchOptions = async () => {
    try {
      const [categoriesRes, typesRes] = await Promise.all([getToolCategories(), getToolTypes()]);

      if (categoriesRes.code === 0) {
        categoryOptions.value = categoriesRes.data.map((item) => ({
          label: item,
          value: item,
        }));
      }

      if (typesRes.code === 0) {
        typeOptions.value = typesRes.data.map((item) => ({
          label: item,
          value: item,
        }));
      }
    } catch (error) {
      console.error('获取选项数据失败:', error);
    }
  };

  const handleCreate = () => {
    currentTool.value = null;
    editDialogVisible.value = true;
  };

  const handleEdit = (tool: Tool) => {
    currentTool.value = { ...tool };
    editDialogVisible.value = true;
  };

  const handleEditSuccess = () => {
    fetchData();
    fetchStatistics();
  };

  const handleDelete = async (tool: Tool) => {
    try {
      await ElMessageBox.confirm(`确定要删除工具 "${tool.name}" 吗？此操作不可恢复。`, '确认删除', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning',
      });

      const response = await deleteTool(tool.id!);
      if (response.code === 0) {
        ElMessage.success('删除成功');
        fetchData();
        fetchStatistics();
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('删除工具失败:', error);
        ElMessage.error('删除失败');
      }
    }
  };

  const handleStatusChange = async (tool: Tool, status: string) => {
    try {
      let response;
      switch (status) {
        case 'online':
          response = await onlineTool(tool.id!);
          break;
        case 'offline':
          response = await offlineTool(tool.id!);
          break;
        case 'maintenance':
          response = await maintenanceTool(tool.id!);
          break;
        default:
          return;
      }

      if (response.code === 0) {
        ElMessage.success('状态更新成功');
        fetchData();
        fetchStatistics();
      }
    } catch (error) {
      console.error('更新状态失败:', error);
      ElMessage.error('更新状态失败');
    }
  };

  const handleSelectionChange = (selection: Tool[]) => {
    selectedTools.value = selection;
  };

  const batchOnline = async () => {
    await batchUpdateStatus('online');
  };

  const batchOffline = async () => {
    await batchUpdateStatus('offline');
  };

  const batchMaintenance = async () => {
    await batchUpdateStatus('maintenance');
  };

  const batchUpdateStatus = async (status: 'online' | 'offline' | 'maintenance') => {
    try {
      const ids = selectedTools.value.map((tool) => tool.id!);
      const response = await batchUpdateToolStatus(ids, status);
      if (response.code === 0) {
        ElMessage.success('批量更新成功');
        fetchData();
        fetchStatistics();
        selectedTools.value = [];
      }
    } catch (error) {
      console.error('批量更新失败:', error);
      ElMessage.error('批量更新失败');
    }
  };

  const batchDelete = async () => {
    try {
      await ElMessageBox.confirm(
        `确定要删除选中的 ${selectedTools.value.length} 个工具吗？此操作不可恢复。`,
        '确认批量删除',
        {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning',
        }
      );

      const ids = selectedTools.value.map((tool) => tool.id!);
      const response = await batchDeleteTools(ids);
      if (response.code === 0) {
        ElMessage.success('批量删除成功');
        fetchData();
        fetchStatistics();
        selectedTools.value = [];
      }
    } catch (error) {
      if (error !== 'cancel') {
        console.error('批量删除失败:', error);
        ElMessage.error('批量删除失败');
      }
    }
  };

  const resetFilters = () => {
    Object.assign(filters, {
      name: '',
      category: '',
      type: '',
      status: '',
    });
    currentPage.value = 1;
    fetchData();
  };

  const handleCurrentChange = (val: number) => {
    currentPage.value = val;
    fetchData();
  };

  const handleSizeChange = (val: number) => {
    pageSize.value = val;
    currentPage.value = 1;
    fetchData();
  };

  // 监听筛选条件变化
  watch(
    filters,
    () => {
      currentPage.value = 1;
      fetchData();
    },
    { deep: true }
  );

  // 初始化
  onMounted(() => {
    fetchData();
    fetchStatistics();
    fetchOptions();
  });
</script>

<style lang="scss" scoped>
  .tool-manage {
    display: flex;
    flex-direction: column;
    height: 100%;
  }

  .header {
    border-bottom: 1px solid #ebeef5;
  }

  .stat-card {
    .stat-content {
      text-align: center;

      .stat-number {
        font-size: 24px;
        font-weight: bold;
        margin-bottom: 4px;
      }

      .stat-label {
        font-size: 14px;
        color: #666;
      }
    }
  }

  .pagination-container {
    padding: 10px;
    border-radius: 6px;
  }
</style>
