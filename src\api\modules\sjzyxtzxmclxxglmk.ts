/*
 * @OriginalName: 数据资源系统中项目处理信息管理模块
 * @Description: 项目处理信息的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_11(data: ApplicationProcessingDTO) {
  return request<RApplicationProcessingVO>(`/applicationProcessing/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_11(data: Array<number>) {
  return request<RListApplicationProcessingVO>(`/applicationProcessing/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_11(data: Array<number>) {
  return request<R>(`/applicationProcessing/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_41(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListApplicationProcessingVO>(`/applicationProcessing/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_42(id: number, params?: { id: number }) {
  return request<RApplicationProcessingVO>(`/applicationProcessing/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_11(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/applicationProcessing/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_41(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/applicationProcessing/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_42(id: number, params?: { id: number }) {
  return request<R>(`/applicationProcessing/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
