/*
 * @OriginalName: 临床数据元数据：数据变量管理模块
 * @Description: 管理元数据中，量表和各列数据变量
 */
import { request } from '@/utils/request';

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_13(data: OriginalColumnDTO) {
  return request<ROriginalColumnVO>(`/OriginalColumn/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_13(data: Array<number>) {
  return request<RListOriginalColumnVO>(`/OriginalColumn/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_13(data: Array<number>) {
  return request<R>(`/OriginalColumn/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 查找该工作表内的所有数据列
 * @description 根据工作表ID，查找该工作表内的所有数据列
 */
export function findOriginalColumnByOriginalSheetId(
  originalSheetId: number,
  pageNum: number,
  pageSize: number,
  params?: { originalSheetId: number; pageNum: number; pageSize: number; searchInput?: string }
) {
  return request<RVOPageOriginalColumnOriginalColumnVO>(
    `/OriginalColumn/findOriginalColumnByOriginalSheetId/${originalSheetId}/${pageNum}/${pageSize}`,
    {
      method: 'get',
      params,
    }
  );
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_45(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListOriginalColumnVO>(`/OriginalColumn/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_46(id: number, params?: { id: number }) {
  return request<ROriginalColumnVO>(`/OriginalColumn/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_13(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/OriginalColumn/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_45(idArray: Array<number>, params?: { idArray: Array<number> }) {
  return request<R>(`/OriginalColumn/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_46(id: number, params?: { id: number }) {
  return request<R>(`/OriginalColumn/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
