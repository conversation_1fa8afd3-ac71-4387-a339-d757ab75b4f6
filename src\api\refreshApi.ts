import axios from 'axios';
import { FORM_CONTENT_TYPE } from './user';

/**
 * 刷新token
 */
export const refreshTokenApi = (refresh_token: string) => {
  const grant_type = 'refresh_token';
  const scope = 'server';
  // 获取当前选中的 basic 认证信息
  const basicAuth = window.sessionStorage.getItem('basicAuth');
  console.log('🚀 ~ refreshTokenApi ~ basicAuth:', basicAuth);
  console.log('🚀 ~ refreshTokenApi ~ FORM_CONTENT_TYPE:', FORM_CONTENT_TYPE);
  return axios.request({
    url: '/auth/oauth2/token',
    headers: {
      skipToken: true,
      Authorization: basicAuth,
      'Content-Type': FORM_CONTENT_TYPE,
    },
    method: 'post',
    data: { refresh_token, grant_type, scope },
    baseURL: '/api',
  });
};
