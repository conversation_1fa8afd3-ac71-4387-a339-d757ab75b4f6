import js from '@eslint/js';
import globals from 'globals';
import tseslint from 'typescript-eslint';
import pluginVue from 'eslint-plugin-vue';
import json from '@eslint/json';
import css from '@eslint/css';
import { defineConfig, globalIgnores } from 'eslint/config';
import { resolve } from 'path';
import { readFileSync } from 'fs';
import unocss from '@unocss/eslint-config/flat';

const autoImportConfigPath = resolve(process.cwd(), '.eslintrc-auto-import.json');
let autoImportGlobals = {};
try {
  const autoImportConfig = JSON.parse(readFileSync(autoImportConfigPath, 'utf-8'));
  autoImportGlobals = autoImportConfig.globals || {};
} catch (error) {
  console.warn(`Failed to load .eslintrc-auto-import.json: ${error.message}`);
}

export default defineConfig([
  unocss,
  { files: ['**/*.{js,mjs,cjs,ts,vue}'], plugins: { js }, extends: ['js/recommended'] },
  { files: ['**/*.{js,mjs,cjs,ts,vue}'], languageOptions: { globals: { ...globals.browser, ...globals.node } } },
  tseslint.configs.recommended,
  pluginVue.configs['flat/essential'],
  { files: ['**/*.vue'], languageOptions: { parserOptions: { parser: tseslint.parser } } },
  { files: ['**/*.json'], plugins: { json }, language: 'json/json', extends: ['json/recommended'] },
  { files: ['**/*.jsonc'], plugins: { json }, language: 'json/jsonc', extends: ['json/recommended'] },
  { files: ['**/*.json5'], plugins: { json }, language: 'json/json5', extends: ['json/recommended'] },
  { files: ['**/*.css'], plugins: { css }, language: 'css/css', extends: ['css/recommended'] },
  { languageOptions: { globals: { ...autoImportGlobals } } },
  {
    rules: {
      'vue/multi-word-component-names': 'off',
      '@typescript-eslint/no-unused-vars': 'off',
      '@typescript-eslint/no-explicit-any': 'off',
      'no-undef': 'off',
      'vue/no-unused-vars': 'off',
    },
  },
  globalIgnores(['**/node_modules/', '.git/', './.vscode/', '**/*.json', '**/*.css', '**/*.scss']),
]);
