/*
 * @OriginalName: 工具管理模块 - 模拟数据
 * @Description: 管理系统中的工具信息，包括工具的增删改查、上架下架等功能（模拟实现）
 */

// 工具数据类型定义
export interface Tool {
  id?: number;
  name: string;
  description?: string;
  category: string;
  type: string;
  provider: string;
  version: string;
  icon?: string;
  status: 'online' | 'offline' | 'maintenance'; // 上架状态
  createTime?: string;
  updateTime?: string;
}

export interface ToolDTO {
  id?: number;
  name: string;
  description?: string;
  category: string;
  type: string;
  provider: string;
  version: string;
  icon?: string;
  status: 'online' | 'offline' | 'maintenance';
}

export interface ToolListParams {
  name?: string;
  category?: string;
  type?: string;
  status?: string;
  pageNum: number;
  pageSize: number;
}

export interface ToolListResponse {
  code: number;
  msg: string;
  data: {
    content: Tool[];
    totalElements: number;
    totalPages: number;
    size: number;
    number: number;
  };
}

// 模拟数据
let mockTools: Tool[] = [
  {
    id: 1,
    name: 'BWA',
    description: '使用-MEM算法将FASTQ读取/配对或未配对映射到参考数据集',
    category: 'Read Mapping',
    type: 'APP',
    provider: '系统',
    version: 'v0.7.17',
    status: 'online',
    icon: '',
    createTime: '2024-01-10 08:00:00',
    updateTime: '2024-01-15 10:30:00',
  },
  {
    id: 2,
    name: 'STAR',
    description: '高速RNA-seq比对工具，支持剪接比对',
    category: 'RNA-Seq',
    type: 'APP',
    provider: '系统',
    version: 'v2.7.10a',
    status: 'online',
    icon: '',
    createTime: '2024-01-09 14:20:00',
    updateTime: '2024-01-14 15:20:00',
  },
  {
    id: 3,
    name: 'Samtools',
    description: 'SAM/BAM文件处理工具集',
    category: 'Mapping Manipuation',
    type: 'APP',
    provider: '系统',
    version: 'v1.15',
    status: 'maintenance',
    icon: '',
    createTime: '2024-01-08 11:10:00',
    updateTime: '2024-01-13 09:15:00',
  },
  {
    id: 4,
    name: 'HISAT2',
    description: '快速敏感的RNA-seq比对工具',
    category: 'RNA-Seq',
    type: 'APP',
    provider: '系统',
    version: 'v2.2.1',
    status: 'online',
    icon: '',
    createTime: '2024-01-07 16:30:00',
    updateTime: '2024-01-12 14:45:00',
  },
  {
    id: 5,
    name: 'Bowtie2',
    description: '快速准确的短序列比对工具',
    category: 'Read Mapping',
    type: 'APP',
    provider: '系统',
    version: 'v2.4.5',
    status: 'offline',
    icon: '',
    createTime: '2024-01-06 09:45:00',
    updateTime: '2024-01-11 11:30:00',
  },
  {
    id: 6,
    name: 'FastQC',
    description: '高通量测序数据质量控制工具',
    category: 'Quality Control',
    type: 'APP',
    provider: '系统',
    version: 'v0.11.9',
    status: 'online',
    icon: '',
    createTime: '2024-01-05 13:20:00',
    updateTime: '2024-01-10 16:15:00',
  },
  {
    id: 7,
    name: 'Trimmomatic',
    description: '灵活的Illumina FASTQ数据修剪工具',
    category: 'Quality Control',
    type: 'APP',
    provider: '系统',
    version: 'v0.39',
    status: 'online',
    icon: '',
    createTime: '2024-01-04 10:30:00',
    updateTime: '2024-01-09 12:40:00',
  },
  {
    id: 8,
    name: 'Cufflinks',
    description: '转录本组装和差异表达分析',
    category: 'RNA-Seq',
    type: 'APP',
    provider: '系统',
    version: 'v2.2.1',
    status: 'maintenance',
    icon: '',
    createTime: '2024-01-03 15:45:00',
    updateTime: '2024-01-08 14:20:00',
  },
];

// 模拟延迟
const delay = (ms: number) => new Promise((resolve) => setTimeout(resolve, ms));

// 生成唯一ID
let nextId = Math.max(...mockTools.map((t) => t.id || 0)) + 1;

/**
 * 获取工具列表（分页）
 * @description 分页查询工具列表，支持按名称、类别、类型、状态筛选
 */
export async function getToolList(params: ToolListParams): Promise<ToolListResponse> {
  await delay(300); // 模拟网络延迟

  let filteredTools = [...mockTools];

  // 筛选逻辑
  if (params.name) {
    filteredTools = filteredTools.filter((tool) => tool.name.toLowerCase().includes(params.name!.toLowerCase()));
  }
  if (params.category) {
    filteredTools = filteredTools.filter((tool) => tool.category === params.category);
  }
  if (params.type) {
    filteredTools = filteredTools.filter((tool) => tool.type === params.type);
  }
  if (params.status) {
    filteredTools = filteredTools.filter((tool) => tool.status === params.status);
  }

  // 分页逻辑
  const totalElements = filteredTools.length;
  const totalPages = Math.ceil(totalElements / params.pageSize);
  const startIndex = (params.pageNum - 1) * params.pageSize;
  const endIndex = startIndex + params.pageSize;
  const content = filteredTools.slice(startIndex, endIndex);

  return {
    code: 0,
    msg: 'success',
    data: {
      content,
      totalElements,
      totalPages,
      size: params.pageSize,
      number: params.pageNum,
    },
  };
}

/**
 * 获取工具详情
 * @description 根据工具ID获取工具详细信息
 */
export async function getToolDetail(id: number): Promise<{ code: number; msg: string; data: Tool }> {
  await delay(200);

  const tool = mockTools.find((t) => t.id === id);
  if (!tool) {
    return {
      code: 404,
      msg: '工具不存在',
      data: {} as Tool,
    };
  }

  return {
    code: 0,
    msg: 'success',
    data: tool,
  };
}

/**
 * 创建工具
 * @description 创建新的工具记录
 */
export async function createTool(data: ToolDTO): Promise<{ code: number; msg: string; data: Tool }> {
  await delay(500);

  const newTool: Tool = {
    ...data,
    id: nextId++,
    createTime: new Date().toLocaleString('zh-CN'),
    updateTime: new Date().toLocaleString('zh-CN'),
  };

  mockTools.push(newTool);

  return {
    code: 0,
    msg: '创建成功',
    data: newTool,
  };
}

/**
 * 更新工具
 * @description 更新工具信息
 */
export async function updateTool(id: number, data: ToolDTO): Promise<{ code: number; msg: string; data: Tool }> {
  await delay(500);

  const index = mockTools.findIndex((t) => t.id === id);
  if (index === -1) {
    return {
      code: 404,
      msg: '工具不存在',
      data: {} as Tool,
    };
  }

  const updatedTool: Tool = {
    ...mockTools[index],
    ...data,
    updateTime: new Date().toLocaleString('zh-CN'),
  };

  mockTools[index] = updatedTool;

  return {
    code: 0,
    msg: '更新成功',
    data: updatedTool,
  };
}

/**
 * 删除工具
 * @description 删除工具记录
 */
export async function deleteTool(id: number): Promise<{ code: number; msg: string }> {
  await delay(300);

  const index = mockTools.findIndex((t) => t.id === id);
  if (index === -1) {
    return {
      code: 404,
      msg: '工具不存在',
    };
  }

  mockTools.splice(index, 1);

  return {
    code: 0,
    msg: '删除成功',
  };
}

/**
 * 批量删除工具
 * @description 批量删除多个工具
 */
export async function batchDeleteTools(ids: number[]): Promise<{ code: number; msg: string }> {
  await delay(500);

  mockTools = mockTools.filter((tool) => !ids.includes(tool.id!));

  return {
    code: 0,
    msg: `批量删除成功，共删除 ${ids.length} 个工具`,
  };
}

/**
 * 上架工具
 * @description 将工具状态设置为上架
 */
export async function onlineTool(id: number): Promise<{ code: number; msg: string; data: Tool }> {
  await delay(300);

  const tool = mockTools.find((t) => t.id === id);
  if (!tool) {
    return {
      code: 404,
      msg: '工具不存在',
      data: {} as Tool,
    };
  }

  tool.status = 'online';
  tool.updateTime = new Date().toLocaleString('zh-CN');

  return {
    code: 0,
    msg: '上架成功',
    data: tool,
  };
}

/**
 * 下架工具
 * @description 将工具状态设置为下架
 */
export async function offlineTool(id: number): Promise<{ code: number; msg: string; data: Tool }> {
  await delay(300);

  const tool = mockTools.find((t) => t.id === id);
  if (!tool) {
    return {
      code: 404,
      msg: '工具不存在',
      data: {} as Tool,
    };
  }

  tool.status = 'offline';
  tool.updateTime = new Date().toLocaleString('zh-CN');

  return {
    code: 0,
    msg: '下架成功',
    data: tool,
  };
}

/**
 * 设置工具为维护状态
 * @description 将工具状态设置为维护中
 */
export async function maintenanceTool(id: number): Promise<{ code: number; msg: string; data: Tool }> {
  await delay(300);

  const tool = mockTools.find((t) => t.id === id);
  if (!tool) {
    return {
      code: 404,
      msg: '工具不存在',
      data: {} as Tool,
    };
  }

  tool.status = 'maintenance';
  tool.updateTime = new Date().toLocaleString('zh-CN');

  return {
    code: 0,
    msg: '设置维护状态成功',
    data: tool,
  };
}

/**
 * 批量更新工具状态
 * @description 批量更新多个工具的状态
 */
export async function batchUpdateToolStatus(
  ids: number[],
  status: 'online' | 'offline' | 'maintenance'
): Promise<{ code: number; msg: string }> {
  await delay(500);

  const statusText = {
    online: '上架',
    offline: '下架',
    maintenance: '维护',
  };

  mockTools.forEach((tool) => {
    if (ids.includes(tool.id!)) {
      tool.status = status;
      tool.updateTime = new Date().toLocaleString('zh-CN');
    }
  });

  return {
    code: 0,
    msg: `批量${statusText[status]}成功，共处理 ${ids.length} 个工具`,
  };
}

/**
 * 获取工具类别列表
 * @description 获取所有可用的工具类别
 */
export async function getToolCategories(): Promise<{ code: number; msg: string; data: string[] }> {
  await delay(100);

  const categories = Array.from(new Set(mockTools.map((tool) => tool.category)));

  return {
    code: 0,
    msg: 'success',
    data: categories,
  };
}

/**
 * 获取工具类型列表
 * @description 获取所有可用的工具类型
 */
export async function getToolTypes(): Promise<{ code: number; msg: string; data: string[] }> {
  await delay(100);

  const types = Array.from(new Set(mockTools.map((tool) => tool.type)));

  return {
    code: 0,
    msg: 'success',
    data: types,
  };
}

/**
 * 上传工具图标
 * @description 上传工具图标文件
 */
export async function uploadToolIcon(file: File): Promise<{ code: number; msg: string; data: { url: string } }> {
  await delay(1000); // 模拟上传时间

  // 模拟生成图标URL
  const mockUrl = `https://via.placeholder.com/64x64/4f46e5/ffffff?text=${encodeURIComponent(file.name.charAt(0).toUpperCase())}`;

  return {
    code: 0,
    msg: '上传成功',
    data: {
      url: mockUrl,
    },
  };
}

/**
 * 获取工具统计信息
 * @description 获取工具的统计数据，用于管理页面展示
 */
export async function getToolStatistics(): Promise<{
  code: number;
  msg: string;
  data: {
    total: number;
    online: number;
    offline: number;
    maintenance: number;
    categoryStats: { category: string; count: number }[];
    typeStats: { type: string; count: number }[];
  };
}> {
  await delay(200);

  const total = mockTools.length;
  const online = mockTools.filter((tool) => tool.status === 'online').length;
  const offline = mockTools.filter((tool) => tool.status === 'offline').length;
  const maintenance = mockTools.filter((tool) => tool.status === 'maintenance').length;

  // 统计类别
  const categoryMap = new Map<string, number>();
  mockTools.forEach((tool) => {
    categoryMap.set(tool.category, (categoryMap.get(tool.category) || 0) + 1);
  });
  const categoryStats = Array.from(categoryMap.entries()).map(([category, count]) => ({
    category,
    count,
  }));

  // 统计类型
  const typeMap = new Map<string, number>();
  mockTools.forEach((tool) => {
    typeMap.set(tool.type, (typeMap.get(tool.type) || 0) + 1);
  });
  const typeStats = Array.from(typeMap.entries()).map(([type, count]) => ({
    type,
    count,
  }));

  return {
    code: 0,
    msg: 'success',
    data: {
      total,
      online,
      offline,
      maintenance,
      categoryStats,
      typeStats,
    },
  };
}
