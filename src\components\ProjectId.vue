<template>
  <div class="flex gap-2">
    <div>{{ modelValue }}</div>
    <el-button @click="handleCopy">
      <el-icon><CopyDocument /></el-icon>
    </el-button>
  </div>
</template>

<script setup lang="ts">
  import { CopyDocument } from '@element-plus/icons-vue';
  import { ElMessage } from 'element-plus';

  interface Props {
    modelValue: string;
  }

  const props = defineProps<Props>();

  const handleCopy = () => {
    navigator.clipboard
      .writeText(props.modelValue)
      .then(() => {
        ElMessage.success('项目ID已复制到剪贴板');
      })
      .catch(() => {
        ElMessage.error('复制失败');
      });
  };
</script>
