<template>
  <div class="account-select-wrapper">
    <el-popover
      v-model:visible="popoverVisible"
      trigger="click"
      placement="bottom-start"
      popper-class="account-select-popover"
      :teleported="true"
      width="400px"
    >
      <template #reference>
        <div class="account-select-trigger" @click.stop>
          <el-input
            :model-value="selectedLabel"
            :placeholder="placeholder"
            readonly
            clearable
            @clear.stop="clearSelection"
          >
            <template #prefix>
              <el-icon><User /></el-icon>
            </template>
            <template #suffix>
              <el-icon class="el-input__icon" :class="{ 'is-reverse': popoverVisible }">
                <ArrowDown />
              </el-icon>
            </template>
          </el-input>
        </div>
      </template>

      <div class="account-select-content">
        <div class="search-header">
          <el-input v-model="searchQuery" placeholder="搜索用户" clearable @input="handleSearch">
            <template #prefix>
              <el-icon><Search /></el-icon>
            </template>
          </el-input>
          <el-button v-if="model" type="primary" link @click.stop="clearSelection"> 清除选中 </el-button>
        </div>
        <div class="account-list">
          <div v-for="(group, index) in groupedAccounts" :key="index" class="account-group">
            <div
              v-for="account in group"
              :key="account.value"
              class="account-item"
              :class="{ active: model === account.value }"
              @click.stop="handleSelect(account)"
            >
              <span class="account-name">{{ account.label }}</span>
            </div>
          </div>
        </div>
      </div>
    </el-popover>
  </div>
</template>

<script setup lang="ts">
  import { User, Search, ArrowDown } from '@element-plus/icons-vue';
  import { ref, computed, watch } from 'vue';

  const model = defineModel<string>();

  interface Props {
    placeholder?: string;
  }

  const { placeholder = '请选择付费账户' } = defineProps<Props>();

  const accounts = ref([
    { label: '张三', value: 'zhangsan' },
    { label: '李四', value: 'lisi' },
    { label: '王五', value: 'wangwu' },
    { label: '赵六', value: 'zhaoliu' },
    { label: '钱七', value: 'qianqi' },
    { label: '孙八', value: 'sunba' },
    { label: '周九', value: 'zhoujiu' },
    { label: '吴十', value: 'wushi' },
    { label: '郑十一', value: 'zhengshiyi' },
    { label: '王十二', value: 'wangshier' },
    { label: '刘十三', value: 'liushisan' },
    { label: '陈十四', value: 'chenshisi' },
  ]);

  const searchQuery = ref('');
  const popoverVisible = ref(false);

  // 当前选中账户的显示名称
  const selectedLabel = computed(() => {
    if (!model.value) return '';
    const selectedAccount = accounts.value.find((account) => account.value === model.value);
    return selectedAccount ? selectedAccount.label : '';
  });

  // 过滤后的账户列表
  const filteredAccounts = computed(() => {
    if (!searchQuery.value) return accounts.value;
    return accounts.value.filter((account) => account.label.toLowerCase().includes(searchQuery.value.toLowerCase()));
  });

  // 将账户列表分成3列
  const groupedAccounts = computed(() => {
    const accounts = filteredAccounts.value;
    const groupSize = Math.ceil(accounts.length / 3);
    return [accounts.slice(0, groupSize), accounts.slice(groupSize, groupSize * 2), accounts.slice(groupSize * 2)];
  });

  const handleSearch = () => {
    // 可以在这里添加防抖逻辑
  };

  const handleSelect = (account: { label: string; value: string }) => {
    model.value = account.value;
    popoverVisible.value = false;
  };

  const clearSelection = (event?: Event) => {
    if (event) {
      event.stopPropagation();
    }
    model.value = '';
  };

  watch(popoverVisible, (visible) => {
    if (!visible) {
      searchQuery.value = '';
    }
  });
</script>

<style lang="scss" scoped>
  .account-select-wrapper {
    width: 100%;
    position: relative;
  }

  .account-select-trigger {
    cursor: pointer;
    width: 100%;
  }

  .account-select-content {
    width: 100%;
    padding: 12px;
  }

  .search-header {
    display: flex;
    align-items: center;
    gap: 8px;
    margin-bottom: 12px;

    .el-input {
      flex: 1;
    }
  }

  .account-list {
    display: grid;
    grid-template-columns: repeat(3, 1fr);
    gap: 8px;
    max-height: 300px;
    overflow-y: auto;
  }

  .account-group {
    display: flex;
    flex-direction: column;
    gap: 8px;
  }

  .account-item {
    display: flex;
    align-items: center;
    padding: 8px;
    border-radius: 4px;
    cursor: pointer;
    transition: all 0.3s;

    &:hover {
      background-color: var(--el-fill-color-light);
    }

    &.active {
      background-color: var(--el-color-primary-light-9);
      color: var(--el-color-primary);
    }
  }

  .account-name {
    font-size: 14px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }

  .is-reverse {
    transform: rotate(180deg);
    transition: transform 0.3s;
  }
</style>
