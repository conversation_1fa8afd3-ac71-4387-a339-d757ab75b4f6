import { ConfigEnv, loadEnv, UserConfig } from 'vite';
import vue from '@vitejs/plugin-vue';
import { resolve } from 'path';
import VueDevTools from 'vite-plugin-vue-devtools';
import autoImport from 'unplugin-auto-import/vite';
import UnoCSS from 'unocss/vite';

// https://vite.dev/config/
export default (mode: ConfigEnv): UserConfig => {
  const root = process.cwd();
  const env = loadEnv(mode.mode, root);

  // 检查命令行参数是否包含 -o 选项
  const shouldOpenBrowser = process.argv.includes('-o') || process.argv.includes('--open');

  return {
    plugins: [
      vue(),
      VueDevTools(),
      UnoCSS(),
      autoImport({
        imports: ['vue', 'vue-router'], // 需要引入的类型来源
        vueTemplate: true,
        eslintrc: {
          enabled: true, // 使用 eslint 配置，第一次生成后关闭，避免重复生成
        },
      }),
    ],
    server: {
      port: env.VITE_PORT as unknown as number,
      open: shouldOpenBrowser,
      proxy: {
        '/resources': {
          //服务器接口路径地址，根据路径设置
          target: env.VITE_PROXY_PATH, //你的服务器地址
          changeOrigin: true, // 允许跨域
        },
        '/api': {
          //服务器接口路径地址，根据路径设置
          target: env.VITE_PROXY_PATH, //你的服务器地址
          changeOrigin: true, // 允许跨域
          rewrite: (path) => path.replace(/^\/api/, ''),
        },
      },
    },
    root, // 项目根目录
    base: mode.command === 'serve' ? './' : env.VITE_PUBLIC_PATH,
    resolve: {
      alias: {
        '@': resolve(__dirname, 'src'), // 路径别名
      },
    },
    css: {
      preprocessorOptions: {
        scss: {
          additionalData: `@use "@/styles/variables.scss" as *;`,
        },
      },
    },
  };
};
