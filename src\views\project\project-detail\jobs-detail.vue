<template>
  <div class="h-full flex flex-col overflow-y-auto">
    <div class="h-70px flex items-center border-b border-#E4E7ED pr-5">
      <h2 class="w-280px flex items-center gap-2 pl-5 text-xl font-bold">
        <el-icon><Loading /></el-icon>
        <div>
          <div class="line-clamp-1">BWA-MEM FASTQ Read</div>
          <div class="text-sm">等待</div>
        </div>
      </h2>

      <el-button :icon="VideoPlay" type="primary" @click="handleStartJob"> 启动新的作业 </el-button>
      <el-button :icon="Document" type="primary" @click="handleViewLog"> 查看日志 </el-button>
      <el-button :icon="Files" type="primary" @click="handleViewIO"> 查看输入输出 </el-button>
      <el-button :icon="InfoFilled" type="primary" @click="handleViewInfo"> 查看信息 </el-button>
      <el-button :icon="CircleClose" type="danger" @click="handleTerminateJob"> 终止作业 </el-button>
    </div>

    <div class="w-full p-5" style="background-color: #f5f7fa">
      <div class="w-full flex rounded bg-white">
        <div class="w-0 flex-1 pl-3">
          <div class="h-50px w-full flex gap-10 overflow-x-auto overflow-y-hidden whitespace-nowrap text-sm text-tip">
            <div
              v-for="(time, index) in timePoints"
              :key="index"
              class="flex-shrink-0 pt-2"
              :class="{ 'pl-2 border-l border-#E4E7ED': index > 0 }"
            >
              {{ time }}
            </div>
          </div>

          <div class="mt-2 w-full flex pb-2">
            <div class="i-uiw:loading"></div>
            <div class="line-clamp-1 ml-3 font-bold">BWA-MEM FASTQ Read Mapper</div>
          </div>
        </div>

        <div class="w-100px text-right">
          <div class="h-40px border-r border-#E6A23C pr-2 text-#E6A23C leading-40px">当前</div>
          <div class="mt-2 pr-2 text-p">--- log</div>
        </div>
      </div>

      <div class="grid grid-cols-2 mt-3 gap-5">
        <div>
          <h4 class="font-bold">输入</h4>
          <ul class="mt-2 flex flex-col gap-3 text-sm">
            <li v-for="(item, index) in inputData" :key="index">
              <div class="text-tip">{{ item.label }}</div>
              <div class="text-p">{{ item.value }}</div>
            </li>
          </ul>
        </div>
        <div>
          <h4 class="font-bold">输出</h4>
          <div class="mt-2 text-sm text-tip">No outputs</div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Loading, VideoPlay, Document, Files, InfoFilled, CircleClose } from '@element-plus/icons-vue';
  import { ref } from 'vue';

  // 测试数据
  const timePoints = ref([
    '01:34:17 PM',
    '01:34:32 PM',
    '01:35:47 PM',
    '01:36:58 PM',
    '01:37:25 PM',
    '01:38:10 PM',
    '01:39:22 PM',
    '01:40:15 PM',
  ]);

  // 输入数据
  const inputData = ref([
    { label: 'Reads (reads_fastqgz)', value: 'SRR100022_20_1.fq.gz' },
    { label: 'Reads (right metas) (reads2_fastqgz)', value: 'SRR100022_20_2.fq.gz' },
    {
      label: 'BWA reference genome index (genomeindex_targz)',
      value: 'Reference Genome Files human gik v37.bwa-index.tar.gz',
    },
  ]);

  const handleStartJob = () => {
    console.log('启动新的作业');
    // 这里添加启动作业的逻辑
  };

  const handleViewLog = () => {
    console.log('查看日志');
    // 这里添加查看日志的逻辑
  };

  const handleViewIO = () => {
    console.log('查看输入输出');
    // 这里添加查看输入输出的逻辑
  };

  const handleViewInfo = () => {
    console.log('查看信息');
    // 这里添加查看信息的逻辑
  };

  const handleTerminateJob = () => {
    console.log('终止作业');
    // 这里添加终止作业的逻辑
  };
</script>
