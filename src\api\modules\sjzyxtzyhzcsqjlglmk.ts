/*
 * @OriginalName: 数据资源系统中用户注册申请记录管理模块
 * @Description: 用户注册申请记录的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_7(data: EnrollmentDTO) {
  return request<REnrollmentVO>(`/enrollment/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_7(data: Array<number>) {
  return request<RListEnrollmentVO>(`/enrollment/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 查找注册申请记录
 * @description 按动态条件，获取满足相应条件的注册记录的基本信息。各条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findEnrollmentByCriteria(data: EnrollmentCriteria) {
  return request<RVOPage>(`/enrollment/findEnrollmentByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_7(data: Array<number>) {
  return request<R>(`/enrollment/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 查找注册申请的处理记录
 * @description 根据注册申请主键ID,查找该注册申请的处理记录。若resolutionType为0，查找处理类型为“注册审核”的记录，若为1查找处理类型为“注册申请”记录.
 */
export function findUserEnrollmentByUserId_1(
  enrollmentId: number,
  resolutionType: number,
  pageNum: number,
  pageSize: number,
  params?: { enrollmentId: number; resolutionType: number; pageNum: number; pageSize: number }
) {
  return request<R>(`/enrollment/findUserEnrollmentByUserId/${enrollmentId}/${resolutionType}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找注册申请的处理记录
 * @description 根据注册申请主键ID,查找该注册申请的全部处理记录。
 */
export function findUserEnrollmentByUserId_2(
  enrollmentId: number,
  pageNum: number,
  pageSize: number,
  params?: { enrollmentId: number; pageNum: number; pageSize: number }
) {
  return request<R>(`/enrollment/findUserEnrollment/${enrollmentId}/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_33(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListEnrollmentVO>(`/enrollment/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_34(id: number, params?: { id: number }) {
  return request<REnrollmentVO>(`/enrollment/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_7(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/enrollment/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_33(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/enrollment/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_34(id: number, params?: { id: number }) {
  return request<R>(`/enrollment/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
