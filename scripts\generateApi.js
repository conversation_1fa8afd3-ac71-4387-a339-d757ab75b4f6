import axios from 'axios';
import fs from 'fs';
import path from 'path';
import prettier from 'prettier';
import pinyin from 'chinese-to-pinyin';
import apiMapping from '../src/api/api_mapping.js';

// 命令行参数配置
const CONFIG = {
  swaggerUrl: process.argv[2], // swagger文档地址
  isSaveSwagger: process.argv.includes('--swagger'), // 是否保留swagger文件
  isPart: process.argv.includes('--part'), // 是否按模块分散生成API文件
  apiDir: path.join(process.cwd(), '/src/api'), // API文件保存路径
  modulesDir: path.join(process.cwd(), '/src/api/modules'), // 模块文件保存路径
  indexPath: path.join(process.cwd(), '/src/api/index.ts'), // 索引文件保存路径
  docPath: path.join(process.cwd(), '/src/api/api.d.ts'), // 类型声明文件保存路径
  apiNameMapping: path.join(process.cwd(), '/src/api_mapping.js'), // 函数名映射表路径
};

// 工具函数
function startsWithHttpOrHttps(str) {
  return str.startsWith('http://') || str.startsWith('https://');
}

function camelCaseString(inputString) {
  if (!inputString) return '';
  return inputString.replace(/-([a-z])/gi, (_, char) => char.toUpperCase());
}

function getLastSegmentAfterSlash(str) {
  return str.split('/').pop() || '';
}

function convertType(property) {
  if (!property) return '';

  // Handle reference type
  if (property.$ref) {
    return getLastSegmentAfterSlash(property.$ref);
  }

  // Handle primitive and array types
  const type = property.type;
  switch (type) {
    case 'integer':
    case 'int32':
    case 'int64':
      return 'number';
    case 'array':
      return `Array<${convertType(property.items)}>`;
    case 'string':
    case 'boolean':
    case 'number':
      return type;
    default:
      return type || '';
  }
}

function convertSchema(schema) {
  if (!schema) return '';

  if (schema.$ref) {
    return getLastSegmentAfterSlash(schema.$ref).replace('[]', '');
  }

  switch (schema.type) {
    case 'array': {
      const itemType = schema.items?.$ref
        ? getLastSegmentAfterSlash(schema.items.$ref)
        : schema.items?.type
          ? convertType(schema.items)
          : '';
      return itemType ? `Array<${itemType}>` : '';
    }

    case 'object': {
      return schema.properties ? getObjectPropertiesString(schema.properties) : '';
    }

    default:
      return convertType(schema).replace('[]', '');
  }
}

function getObjectPropertiesString(properties) {
  if (!properties || typeof properties !== 'object') {
    return '{}';
  }

  const entries = Object.entries(properties)
    .map(([key, value]) => `${key}:${convertType(value)}`)
    .join(',');

  return `{${entries}}`;
}

function convertParameters(parameters) {
  if (!Array.isArray(parameters) || parameters.length === 0) {
    return '{}';
  }

  const paramStrings = parameters.map(
    (item) => `${item.name}${item.required ? '' : '?'}:${convertSchema(item.schema)}`
  );

  return `{${paramStrings.join(',')}}`;
}

function getRequestUrlAndParams(str, operation) {
  // 处理URL中的路径参数
  const regex = /\/\{([^}]*)\}/g;
  const result = str.replace(regex, (_, param) => `/$\{${param}}`);
  const pathParams = Array.from(str.matchAll(regex)).map((match) => match[1]);

  const requestParams = [];
  const functionParamsList = [...pathParams];

  // 处理请求体参数
  const schema = operation.requestBody?.content['application/json']?.schema;
  if (schema) {
    const bodyReq = 'data:' + convertSchema(schema);
    functionParamsList.push(bodyReq);
    requestParams.push('data');
  }

  // 处理查询参数
  if (operation.parameters?.length) {
    functionParamsList.push('params?:' + convertParameters(operation.parameters));
    requestParams.push('params');

    // 更新路径参数的类型
    const pathParamsWithTypes = pathParams.map((param) => {
      const parameter = operation.parameters.find((p) => p.name === param);
      return parameter ? `${param}:${convertSchema(parameter.schema)}` : param;
    });
    functionParamsList.splice(0, pathParams.length, ...pathParamsWithTypes);
  }

  return {
    requestUrl: `\`${result}\``,
    functionParams: functionParamsList.join(','),
    requestParams,
  };
}

function getResponsesType(responses) {
  const successResponse = responses['200']?.content?.['*/*']?.schema;
  if (!successResponse) {
    return '';
  }
  const type = convertSchema(successResponse);
  return type ? `<${type}>` : '';
}

// 文件操作函数
async function saveFile(filePath, content) {
  try {
    const prettierConfig = {
      parser: 'typescript',
      semi: true,
      tabWidth: 2,
      singleQuote: true,
      printWidth: 120,
      trailingComma: 'es5',
      vueIndentScriptAndStyle: true,
      endOfLine: 'crlf',
      bracketSpacing: true,
    };

    const formattedContent = await prettier.format(content, prettierConfig);
    await fs.promises.writeFile(filePath, formattedContent);
  } catch (error) {
    console.error(`保存文件失败 ${filePath}:`, error);
    throw error;
  }
}

async function deleteFile(filePath) {
  try {
    await fs.promises.unlink(filePath);
  } catch (err) {
    console.error(`无法删除文件 ${filePath}: ${err}`);
    throw err;
  }
}

// API生成相关函数
function generateApiCode(swaggerData, tag = '') {
  const { paths } = swaggerData;
  const apiCode = [];

  // 遍历所有API路径和方法
  Object.entries(paths).forEach(([path, methods]) => {
    Object.entries(methods).forEach(([method, operation]) => {
      // 如果指定了tag但当前operation不属于该tag则跳过
      if (tag && !operation.tags.includes(tag)) {
        return;
      }

      const { summary = '', description = '', operationId, responses } = operation;

      // 提取映射中的所有函数名
      const names = Object.values(apiMapping);
      let functionName = camelCaseString(operationId);
      if (apiMapping[path]) {
        // 尝试从映射中获取函数名
        functionName = apiMapping[path];
      } else if (names.includes(functionName)) {
        // 检查生成的函数名是否已经存在于映射中，如果存在则添加后缀
        functionName += '_02';
      }

      const { requestUrl, requestParams, functionParams } = getRequestUrlAndParams(path, operation);
      const resType = getResponsesType(responses);

      // 构建请求配置对象
      const config = `{
        method: '${method}',
        ${requestParams.join(',')}
      }`;

      // 生成函数定义
      const functionBody = `/**
      * ${summary}
      * @description ${description}
      */
      export function ${functionName}(${functionParams}) {
        return request${resType}(${requestUrl}, ${config});
      }`;

      apiCode.push(functionBody);
    });
  });

  return apiCode.join('\n\n');
}

async function generateSeparateApiModules(swaggerData) {
  const indexContent = [];

  try {
    for (const { name, description } of swaggerData.tags) {
      // 去除冒号和空格
      const processedName = name.replace(/[：\s]/g, '');
      // 生成拼音
      const pinyinName = pinyin(processedName, { firstCharacter: true, removeSpace: true, keepRest: true });
      const filePath = path.join(CONFIG.modulesDir, `/${pinyinName}.ts`);

      const fileContent = [
        `/*\n * @OriginalName: ${name}\n * @Description: ${description}\n  */`,
        `import { request } from '@/utils/request';`,
        '',
        generateApiCode(swaggerData, name),
      ].join('\n');

      await saveFile(filePath, fileContent);
      indexContent.push(`export * from './modules/${pinyinName}';`);
      console.log(`生成API文件: ${filePath} (原名: ${name})`);
    }

    await saveFile(CONFIG.indexPath, indexContent.join('\n'));
    console.log(`生成索引文件: ${CONFIG.indexPath}`);
  } catch (error) {
    console.error('生成API文件时出错:', error);
    throw error;
  }
}

async function generateUnifiedApiFile(swaggerData) {
  // 根据swagger文件生成接口代码
  const apiContent = generateApiCode(swaggerData);

  // 添加文件头部导入语句
  const fileHeader = `
  import { request } from '@/utils/request'
  import { apiMapping } from './../src/api/api_mapping';
`;

  // 组合完整的文件内容
  const fileContent = `${fileHeader}${apiContent}`;

  // 保存文件
  await saveFile(CONFIG.indexPath, fileContent);
  console.log(`已生成统一API文件: ${CONFIG.indexPath}`);
}

async function tsDoc(swaggerData) {
  const schemas = swaggerData.components?.schemas;
  if (!schemas) {
    console.warn('未定义schemas，创建ts类型文件失败');
    return '';
  }

  const typeDefinitions = Object.entries(schemas)
    .map(([key, item]) => {
      if (item.type !== 'object') {
        console.warn(`类型${key}未生成ts类型声明，仅支持object类型`);
        return '';
      }

      const properties = item.properties;
      if (!properties || !Object.keys(properties).length) {
        return '';
      }

      // 处理数组类型名称，如 RByte[] -> RByte
      const interfaceName = key.replace('[]', '');

      const propertyDefinitions = Object.entries(properties)
        .map(([propKey, prop]) => {
          const optional = prop.required ? '' : '?';
          const type = convertType(prop);
          const description = prop.description ? ` // ${prop.description}` : '';
          return `  ${propKey}${optional}: ${type};${description}`;
        })
        .join('\n');

      return `declare interface ${interfaceName} {\n${propertyDefinitions}\n}`;
    })
    .filter(Boolean)
    .join('\n\n');

  await saveFile(CONFIG.docPath, typeDefinitions);
  console.log(`类型定义已保存至 ${CONFIG.docPath}`);
}

async function downloadSwagger(swaggerUrl) {
  try {
    // 获取swagger文档
    const { data } = await axios.get(swaggerUrl);

    // 生成文件名和路径
    const fileName = data.info.title.replace(/\s+/g, '') + '.json';
    const filePath = path.join(CONFIG.apiDir, fileName);

    // 格式化并写入文件
    const jsonContent = JSON.stringify(data, null, 2);
    fs.writeFileSync(filePath, jsonContent);

    // 打印保存信息
    if (CONFIG.isSaveSwagger) {
      console.log(`保存swagger文件: ${filePath}`);
    }

    return filePath;
  } catch (error) {
    console.error('获取Swagger文档失败:', error);
    process.exit(1);
  }
}

// 校验swagger文档地址
if (!CONFIG.swaggerUrl) {
  console.error('请提供swagger文档地址');
  console.log(`示例:
    node generateApi.js 'http://*************:9999/resources/v3/api-docs'
    或
    node .\\scripts\\generateApi.js 'D:\\code\\cbd-ui\\src\\api\\PigSwagger API.json'
  `);
  process.exit(1);
}

// 创建API文件夹
if (!fs.existsSync(CONFIG.apiDir)) {
  fs.mkdirSync(CONFIG.apiDir);
}

// 主函数
async function main() {
  try {
    console.log('swagger文档地址:', CONFIG.swaggerUrl);

    // 获取swagger文件路径
    const swaggerFilePath = startsWithHttpOrHttps(CONFIG.swaggerUrl)
      ? await downloadSwagger(CONFIG.swaggerUrl)
      : CONFIG.swaggerUrl;

    // 本地文件不删除
    if (!startsWithHttpOrHttps(CONFIG.swaggerUrl)) {
      CONFIG.isSaveSwagger = true;
    }

    // 重置modules文件夹
    if (fs.existsSync(CONFIG.modulesDir)) {
      fs.rmSync(CONFIG.modulesDir, { recursive: true });
    }
    fs.mkdirSync(CONFIG.modulesDir, { recursive: true });

    // 解析swagger数据
    const swaggerData = JSON.parse(fs.readFileSync(swaggerFilePath, 'utf8'));

    // 生成类型声明文件
    await tsDoc(swaggerData);

    // 生成API文件
    await (CONFIG.isPart ? generateSeparateApiModules : generateUnifiedApiFile)(swaggerData);

    // 清理临时文件
    if (!CONFIG.isSaveSwagger) {
      await deleteFile(swaggerFilePath);
    }
  } catch (error) {
    console.error('生成API文件失败:', error);
    process.exit(1);
  }
}

main();
