import type { Router } from 'vue-router';
import { ElMessage } from 'element-plus';
import { useUsers } from '@/store/user-info';

/**
 * 设置路由守卫
 * @param router 路由实例
 */
export function setupRouterGuards(router: Router) {
  // 全局前置守卫
  router.beforeEach((to, from, next) => {
    const userStore = useUsers();
    
    // 检查路由是否需要认证
    if (to.meta?.requiresAuth) {
      // 检查用户是否已登录
      if (!userStore.isLogin) {
        ElMessage.warning('请先登录');
        next('/login');
        return;
      }
      
      // 检查用户角色权限
      if (to.meta?.roles && Array.isArray(to.meta.roles)) {
        const userRoles = userStore.user.roleCode;
        const hasPermission = to.meta.roles.some(role => userRoles.includes(role));
        
        if (!hasPermission) {
          ElMessage.error('您没有权限访问此页面');
          next(from.path || '/');
          return;
        }
      }
    }
    
    next();
  });
  
  // 全局后置钩子
  router.afterEach((to) => {
    // 设置页面标题
    if (to.meta?.title) {
      document.title = `${to.meta.title} - IRAP`;
    } else {
      document.title = 'IRAP';
    }
  });
}
