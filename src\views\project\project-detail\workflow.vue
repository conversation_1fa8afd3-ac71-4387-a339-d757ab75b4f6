<template>
  <div class="h-full overflow-y-auto">
    <div style="background-color: #f5f7fa">
      <div class="pl-5 bg-white flex h-60px items-center">
        <div class="flex gap-2 items-center">
          <a class="text-#999 flex gap-2 cursor-pointer items-center" @click="router.back()">
            <el-icon size="20px"><ArrowLeftBold /></el-icon>
          </a>
          <div class="text-m flex gap-2 items-center">
            <div class="i-mdi:workflow"></div>
            <el-input v-model="name" placeholder="工作流名称"></el-input>
          </div>
        </div>
      </div>

      <div class="min-h-300px">
        <div class="pt-3 flex justify-center">
          <ul class="text-tip gap-4 grid grid-cols-3 w-2/3">
            <li class="flex items-center justify-center">
              输入
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </li>
            <li class="flex items-center justify-center">
              应用
              <el-icon class="ml-2"><ArrowRight /></el-icon>
            </li>
            <li class="flex justify-center">输出</li>
          </ul>
        </div>

        <div class="mt-6 flex justify-center">
          <el-button type="default" size="large" :icon="Plus" @click="onAddStep">增加步骤</el-button>
        </div>
      </div>
    </div>
  </div>

  <!-- 添加工具选择对话框 -->
  <el-dialog v-model="dialogVisible" title="选择工具" width="600px" :before-close="handleClose">
    <div class="mb-4">
      <el-input v-model="searchKeyword" placeholder="搜索工具" prefix-icon="Search" clearable />
    </div>

    <el-tabs v-model="activeTab">
      <el-tab-pane label="已安装应用" name="installed">
        <el-checkbox-group v-model="selectedTools">
          <el-scrollbar height="300px">
            <div class="gap-3 grid grid-cols-2">
              <el-checkbox v-for="tool in filteredInstalledTools" :key="tool.id" :label="tool.id" border class="w-full">
                <div class="flex gap-2 items-center">
                  <span>{{ tool.name }}</span>
                </div>
              </el-checkbox>
            </div>
          </el-scrollbar>
        </el-checkbox-group>
      </el-tab-pane>

      <el-tab-pane label="自定义应用" name="custom">
        <el-checkbox-group v-model="selectedTools">
          <el-scrollbar height="300px">
            <div class="gap-3 grid grid-cols-2">
              <el-checkbox v-for="tool in filteredCustomTools" :key="tool.id" :label="tool.id" border class="w-full">
                <div class="flex gap-2 items-center">
                  <span>{{ tool.name }}</span>
                </div>
              </el-checkbox>
            </div>
          </el-scrollbar>
        </el-checkbox-group>
      </el-tab-pane>
    </el-tabs>

    <template #footer>
      <div class="dialog-footer">
        <el-button @click="dialogVisible = false">取消</el-button>
        <el-button type="primary" @click="confirmSelection">确定</el-button>
      </div>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ArrowLeftBold, Plus, ArrowRight, Search } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';
  import { ref, computed } from 'vue';

  const router = useRouter();
  const name = ref('工作流1');

  // 对话框相关状态
  const dialogVisible = ref(false);
  const activeTab = ref('installed');
  const searchKeyword = ref('');
  const selectedTools = ref<string[]>([]);

  // 模拟工具数据
  const installedTools = ref([
    { id: '1', name: '数据分析', icon: 'https://via.placeholder.com/30', type: 'installed' },
    { id: '2', name: '图像处理', icon: 'https://via.placeholder.com/30', type: 'installed' },
    { id: '3', name: '文本提取', icon: 'https://via.placeholder.com/30', type: 'installed' },
    { id: '4', name: '语音识别', icon: 'https://via.placeholder.com/30', type: 'installed' },
    { id: '5', name: '机器翻译', icon: 'https://via.placeholder.com/30', type: 'installed' },
    { id: '6', name: '数据可视化', icon: 'https://via.placeholder.com/30', type: 'installed' },
  ]);

  const customTools = ref([
    { id: '7', name: '自定义工具1', icon: 'https://via.placeholder.com/30', type: 'custom' },
    { id: '8', name: '自定义工具2', icon: 'https://via.placeholder.com/30', type: 'custom' },
    { id: '9', name: '自定义工具3', icon: 'https://via.placeholder.com/30', type: 'custom' },
  ]);

  // 根据搜索关键词过滤工具
  const filteredInstalledTools = computed(() => {
    if (!searchKeyword.value) return installedTools.value;
    return installedTools.value.filter((tool) => tool.name.toLowerCase().includes(searchKeyword.value.toLowerCase()));
  });

  const filteredCustomTools = computed(() => {
    if (!searchKeyword.value) return customTools.value;
    return customTools.value.filter((tool) => tool.name.toLowerCase().includes(searchKeyword.value.toLowerCase()));
  });

  // 处理关闭对话框
  const handleClose = () => {
    dialogVisible.value = false;
    searchKeyword.value = '';
    selectedTools.value = [];
  };

  // 确认选择
  const confirmSelection = () => {
    // 这里可以处理选中的工具
    console.log('选中的工具IDs:', selectedTools.value);

    // 获取选中的工具详细信息
    const allTools = [...installedTools.value, ...customTools.value];
    const selectedToolsInfo = allTools.filter((tool) => selectedTools.value.includes(tool.id));
    console.log('选中的工具详情:', selectedToolsInfo);

    // 关闭对话框
    dialogVisible.value = false;
    // 清空搜索和选择
    searchKeyword.value = '';
    selectedTools.value = [];
  };

  // 打开添加步骤对话框
  const onAddStep = () => {
    dialogVisible.value = true;
  };
</script>
