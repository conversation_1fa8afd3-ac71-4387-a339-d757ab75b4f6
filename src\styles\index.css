html,
body,
#app {
  height: 100%;
}

/* 自定义样式 */
.el-table {
  --el-table-header-text-color: #303333;
  .el-table__cell {
    padding: 12px 0;
  }
}

.c-table-header {
  --el-table-header-bg-color: #f0f2f5;
}

.el-menu--horizontal .el-menu-item:not(.is-disabled):hover,
.el-menu--horizontal .el-menu-item:not(.is-disabled):focus {
  background-color: transparent;
}

.el-link.el-link--primary,
.el-button--primary.is-link {
  color: #2979ff;
}
.el-link.el-link--primary:hover,
.el-link.el-link--primary:focus,
.el-button.is-link:hover,
.el-button.is-link:focus {
  color: #9bb8f0;
}

.el-button:focus-visible {
  outline: none;
}

.pagination-bottom {
  z-index: 1;
  display: flex;
  align-items: center;
  justify-content: flex-end;
  padding-right: 40px;
  width: 100%;
  height: 56px;
  background: #ffffff;
  box-shadow: var(--el-box-shadow-light);
}
