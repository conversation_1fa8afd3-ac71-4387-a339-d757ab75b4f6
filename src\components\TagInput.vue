<template>
  <div class="flex flex-wrap gap-2">
    <el-tag v-for="tag in modelValue" :key="tag" closable @close="handleDeleteTag(tag)">
      {{ tag }}
    </el-tag>
    <el-input
      v-if="inputVisible"
      ref="tagInputRef"
      v-model="inputValue"
      class="w-100px"
      size="small"
      @keyup.enter="handleInputConfirm"
      @blur="handleInputConfirm"
    />
    <el-button v-else class="button-new-tag" size="small" @click="showInput"> + 新标签 </el-button>
  </div>
</template>

<script setup lang="ts">
  const modelValue = defineModel<string[]>({ required: true });

  const inputVisible = ref(false);
  const inputValue = ref('');
  const tagInputRef = ref();

  const showInput = () => {
    inputVisible.value = true;
    nextTick(() => {
      tagInputRef.value?.input?.focus();
    });
  };

  const handleInputConfirm = () => {
    if (inputValue.value) {
      if (!modelValue.value.includes(inputValue.value)) {
        modelValue.value = [...modelValue.value, inputValue.value];
      }
    }
    inputVisible.value = false;
    inputValue.value = '';
  };

  const handleDeleteTag = (tag: string) => {
    modelValue.value = modelValue.value.filter((t) => t !== tag);
  };
</script>
