<template>
  <el-dialog v-model="modelValue" :title="`信息 ${fileInfo.name}`" width="600px">
    <el-form label-width="100px">
      <el-form-item label="名称">
        <el-input v-model="form.name" />
      </el-form-item>
      <el-form-item label="路径">
        <div>{{ fileInfo.path }}</div>
      </el-form-item>
      <el-form-item label="类型">
        <div>{{ fileInfo.type }}</div>
      </el-form-item>
      <el-form-item label="ID">
        <div>{{ fileInfo.id }}</div>
      </el-form-item>
      <el-form-item label="大小">
        <div>{{ fileInfo.size }}</div>
      </el-form-item>
      <el-form-item label="创建用户">
        <div>{{ fileInfo.creator }}</div>
      </el-form-item>
      <el-form-item label="创建时间">
        <div>{{ fileInfo.createTime }}</div>
      </el-form-item>
      <el-form-item label="修改时间">
        <div>{{ fileInfo.updateTime }}</div>
      </el-form-item>
      <el-form-item label="标签">
        <TagInput v-model="form.tags" />
      </el-form-item>
      <el-form-item label="特性">
        <FeatureList style="width: 320px" v-model="form.features" />
      </el-form-item>
    </el-form>
    <template #footer>
      <span class="dialog-footer">
        <el-button type="default" @click="handleCancel">取消</el-button>
        <el-button type="primary" @click="handleSave">保存</el-button>
      </span>
    </template>
  </el-dialog>
</template>

<script setup lang="ts">
  import { ref, watch } from 'vue';
  import FeatureList from '@/components/FeatureList.vue';
  import TagInput from '@/components/TagInput.vue';

  const modelValue = defineModel<boolean>();
  interface Props {
    fileInfo: FileInfo;
  }
  const { fileInfo } = defineProps<Props>();

  const emit = defineEmits<{ success: [] }>();

  const form = ref({
    name: fileInfo?.name || '',
    tags: [...(fileInfo?.tags || [])],
    features: [...(fileInfo?.features || [])],
  });

  // 监听 fileInfo 的变化
  watch(
    () => fileInfo,
    (newFileInfo) => {
      if (newFileInfo) {
        form.value = {
          name: newFileInfo.name || '',
          tags: [...(newFileInfo.tags || [])],
          features: [...(newFileInfo.features || [])],
        };
      }
    },
    { immediate: true, deep: true }
  );

  const handleCancel = () => {
    modelValue.value = false;
  };

  const handleSave = () => {
    modelValue.value = false;
    console.log(form);
    emit('success');
  };
</script>

<style lang="scss" scoped>
  :deep(.el-form-item) {
    margin-bottom: 18px;
  }

  :deep(.el-form-item__label) {
    padding-right: 12px;
  }
</style>
