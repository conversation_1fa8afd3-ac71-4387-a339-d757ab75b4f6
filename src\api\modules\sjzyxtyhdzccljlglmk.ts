/*
 * @OriginalName: 数据资源系统用户的注册处理记录管理模块
 * @Description: 数据资源系统用户的注册处理记录的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity(data: UserEnrollmentDTO) {
  return request<RUserEnrollmentVO>(`/userEnrollment/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找注册申请处理记录
 * @description 按动态条件，获取满足相应条件的注册申请的处理记录。各条件按与操作进行模糊查询。所有条件均为空时，返回全部记录。
 */
export function findUserEnrollmentByCriteria(data: UserEnrollmentCriteria) {
  return request<RVOPage>(`/userEnrollment/findUserEnrollmentByCriteria`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById(data: Array<UserenrollmentId>) {
  return request<RListUserEnrollmentVO>(`/userEnrollment/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById(data: Array<UserenrollmentId>) {
  return request<R>(`/userEnrollment/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_19(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListUserEnrollmentVO>(`/userEnrollment/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_20(id: UserenrollmentId, params?: { id: UserenrollmentId }) {
  return request<RUserEnrollmentVO>(`/userEnrollment/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/userEnrollment/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_19(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/userEnrollment/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_20(id: UserenrollmentId, params?: { id: UserenrollmentId }) {
  return request<R>(`/userEnrollment/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
