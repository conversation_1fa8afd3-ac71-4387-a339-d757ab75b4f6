<template>
  <el-cascader
    v-model="model"
    style="width: 100%"
    :options="regionOptions"
    :placeholder="placeholder"
    :props="regionProps"
  />
</template>

<script setup lang="ts">
  import { provinceAndCityData } from 'element-china-area-data';
  import type { CascaderValue } from 'element-plus';

  const model = defineModel<CascaderValue>({ default: [] });

  interface Props {
    placeholder?: string;
  }
  const { placeholder = '请选择地区' } = defineProps<Props>();

  const regionOptions = ref(provinceAndCityData);
  const regionProps = {
    value: 'value',
    label: 'label',
    children: 'children',
  };
</script>
