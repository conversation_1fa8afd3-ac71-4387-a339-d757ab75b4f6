/*
 * @OriginalName: 临床数据元数据：量表信息管理模块
 * @Description: 管理元数据中，量表和各列数据变量
 */
import { request } from '@/utils/request';

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_12(data: OriginalSheetDTO) {
  return request<ROriginalSheetVO>(`/OriginalSheet/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_12(data: Array<number>) {
  return request<RListOriginalSheetVO>(`/OriginalSheet/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_12(data: Array<number>) {
  return request<R>(`/OriginalSheet/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 发布量表的元数据
 * @description 根据医学数据集中量表的ID，发布该量表的元数据
 */
export function publishOriginalSheetById_02(params?: { originalSheetId: number; projectCode: string }) {
  return request<R>(`/OriginalSheet/publishOriginalSheetById`, {
    method: 'get',
    params,
  });
}

/**
 * 查找量表的元数据
 * @description 根据医学数据集ID，查找医学数据集所有量表的元数据
 */
export function findOriginalSheetByFileId(
  fileId: number,
  pageNum: number,
  pageSize: number,
  params?: { fileId: number; pageNum: number; pageSize: number; searchInput?: string }
) {
  return request<RVOPageOriginalSheetOriginalSheetVO>(
    `/OriginalSheet/findOriginalSheetByFileId/${fileId}/${pageNum}/${pageSize}`,
    {
      method: 'get',
      params,
    }
  );
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_43(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListOriginalSheetVO>(`/OriginalSheet/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_44(id: number, params?: { id: number }) {
  return request<ROriginalSheetVO>(`/OriginalSheet/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_12(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/OriginalSheet/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_43(idArray: Array<number>, params?: { idArray: Array<number> }) {
  return request<R>(`/OriginalSheet/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_44(id: number, params?: { id: number }) {
  return request<R>(`/OriginalSheet/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 清空量表元数据
 * @description 根据量表ID，清空量表的元数据，包括量表所描述的数据。
 */
export function clearOriginalSheetAndDatasetById(originalSheetId: number, params?: { originalSheetId: number }) {
  return request<R>(`/OriginalSheet/clearOriginalSheetAndDatasetById/${originalSheetId}`, {
    method: 'get',
    params,
  });
}

/**
 * 测试SSE
 * @description 测试SSE
 */
export function sseTest(fileId: number, cbdDatabaseId: number, params?: { fileId: number; cbdDatabaseId: number }) {
  return request(`/OriginalSheet/SSE/${fileId}/${cbdDatabaseId}`, {
    method: 'get',
    params,
  });
}
