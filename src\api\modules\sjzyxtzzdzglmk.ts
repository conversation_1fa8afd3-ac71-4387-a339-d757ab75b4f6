/*
 * @OriginalName: 数据资源系统中字典值管理模块
 * @Description: 医学数据资源系统字典值信息的创建、更新和查询
 */
import { request } from '@/utils/request';

/**
 * 新建或更新数据表中的记录
 * @description 按tdto的信息，新建或更新数据表中的记录。
 */
export function newOrUpdateEntity_16(data: DictionaryValueDTO) {
  return request<RDictionaryValueVO>(`/DictionaryValue/newOrUpdateEntity`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_16(data: Array<number>) {
  return request<RListDictionaryValueVO>(`/DictionaryValue/findEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_16(data: Array<number>) {
  return request<R>(`/DictionaryValue/deleteEntityByIdCollection`, {
    method: 'post',
    data,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条或多条数据记录。
 */
export function findEntityById_51(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<RListDictionaryValueVO>(`/DictionaryValue/findEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 查找数据表中的记录
 * @description 按数据表的主键，精确查找一条数据记录。
 */
export function findEntityById_52(id: number, params?: { id: number }) {
  return request<RDictionaryValueVO>(`/DictionaryValue/findEntityById/${id}`, {
    method: 'get',
    params,
  });
}

/**
 * 查询字典中的值
 * @description  按字典id，分页式返回某字典项中的所有字典值
 */
export function findByState(
  dictionaryId: number,
  pageNum: number,
  pageSize: number,
  params?: { dictionaryId: number; pageNum: number; pageSize: number }
) {
  return request<RVOPageDictionaryValueDictionaryValueVO>(
    `/DictionaryValue/findDictionaryValue/${dictionaryId}/${pageNum}/${pageSize}`,
    {
      method: 'get',
      params,
    }
  );
}

/**
 * 查询字典中的值
 * @description 按字典的代码，分页式返回某字典项中的所有字典值。
 */
export function findByBictionaryCode(
  pageNum: number,
  pageSize: number,
  params?: { dictionaryCode?: string; pageNum: number; pageSize: number }
) {
  return request<RVOPageDictionaryValueDictionaryVO>(`/DictionaryValue/findByBictionaryCode/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 显示数据表中的全部记录
 * @description 分页显示数据表中的全部记录
 */
export function findAll_16(pageNum: number, pageSize: number, params?: { pageNum: number; pageSize: number }) {
  return request<R>(`/DictionaryValue/findAll/${pageNum}/${pageSize}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一或多条记录。
 */
export function deleteEntityById_51(idArray: Array<object>, params?: { idArray: Array<object> }) {
  return request<R>(`/DictionaryValue/deleteEntityByIdArray/${idArray}`, {
    method: 'get',
    params,
  });
}

/**
 * 删除数据表中的记录
 * @description 按主键Id，删除数据表中的一条记录。
 */
export function deleteEntityById_52(id: number, params?: { id: number }) {
  return request<R>(`/DictionaryValue/deleteEntityById/${id}`, {
    method: 'get',
    params,
  });
}
