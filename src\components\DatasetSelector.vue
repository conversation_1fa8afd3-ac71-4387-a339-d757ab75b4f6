<template>
  <div class="dataset-selector">
    <div class="selector-header">
      <el-button type="primary" @click="dialogVisible = true">选择数据集</el-button>
      <span class="selected-count">已选择 {{ selectedDatasets.length }} 个数据集</span>
    </div>

    <!-- 已选择的数据集列表 -->
    <div v-if="selectedDatasets.length > 0" class="selected-datasets">
      <div class="selected-header">已选择的数据集：</div>
      <div class="selected-list">
        <el-tag
          v-for="dataset in selectedDatasets"
          :key="dataset.id"
          closable
          @close="removeDataset(dataset.id)"
          class="dataset-tag"
        >
          {{ dataset.name }} ({{ dataset.subjectCode }})
        </el-tag>
      </div>
    </div>

    <!-- 数据集选择弹窗 -->
    <el-dialog v-model="dialogVisible" title="选择数据集" width="800px" :before-close="handleClose">
      <div class="dialog-content">
        <!-- 搜索栏 -->
        <div class="search-bar">
          <el-input
            v-model="searchKeyword"
            placeholder="搜索数据集名称或课题编码"
            clearable
            :prefix-icon="Search"
            @input="handleSearch"
          />
        </div>

        <!-- 数据集表格 -->
        <el-table
          ref="tableRef"
          :data="filteredDatasets"
          @selection-change="handleSelectionChange"
          height="400"
          class="dataset-table"
        >
          <el-table-column type="selection" width="55" :selectable="isSelectable" />
          <el-table-column prop="name" label="数据集名称" min-width="150" />
          <el-table-column prop="subjectCode" label="课题编码" min-width="120" />
          <el-table-column prop="description" label="描述" min-width="200" show-overflow-tooltip />
          <el-table-column prop="size" label="数据量" width="100" />
          <el-table-column prop="createTime" label="创建时间" width="120" />
        </el-table>

        <!-- 分页 -->
        <div class="pagination-wrapper">
          <el-pagination
            v-model:current-page="currentPage"
            v-model:page-size="pageSize"
            :page-sizes="[10, 20, 50]"
            :total="total"
            layout="total, sizes, prev, pager, next"
            @size-change="handleSizeChange"
            @current-change="handleCurrentChange"
          />
        </div>
      </div>

      <template #footer>
        <div class="dialog-footer">
          <el-button @click="handleClose">取消</el-button>
          <el-button type="primary" @click="confirmSelection">
            确定 (已选择 {{ tempSelectedDatasets.length }} 个)
          </el-button>
        </div>
      </template>
    </el-dialog>
  </div>
</template>

<script setup lang="ts">
import { ref, reactive, computed, watch, nextTick } from 'vue'
import { Search } from '@element-plus/icons-vue'
import { ElMessage } from 'element-plus'

// 数据集接口定义
interface Dataset {
  id: number
  name: string
  subjectCode: string
  description: string
  size: string
  createTime: string
}

// Props
interface Props {
  modelValue: Dataset[]
  multiple?: boolean
}

const props = withDefaults(defineProps<Props>(), {
  multiple: true
})

// Emits
const emit = defineEmits<{
  'update:modelValue': [value: Dataset[]]
}>()

// 响应式数据
const dialogVisible = ref(false)
const searchKeyword = ref('')
const currentPage = ref(1)
const pageSize = ref(10)
const total = ref(0)
const tableRef = ref()

// 选中的数据集
const selectedDatasets = ref<Dataset[]>([...props.modelValue])
const tempSelectedDatasets = ref<Dataset[]>([])

// 模拟数据集数据
const mockDatasets = ref<Dataset[]>([
  {
    id: 1,
    name: 'TCGA-BRCA',
    subjectCode: 'PROJ001',
    description: '乳腺癌基因组数据集',
    size: '2.5GB',
    createTime: '2024-01-15'
  },
  {
    id: 2,
    name: 'TCGA-LUAD',
    subjectCode: 'PROJ002',
    description: '肺腺癌基因组数据集',
    size: '1.8GB',
    createTime: '2024-01-20'
  },
  {
    id: 3,
    name: 'GTEx-Brain',
    subjectCode: 'PROJ003',
    description: '脑组织表达数据集',
    size: '3.2GB',
    createTime: '2024-02-01'
  },
  {
    id: 4,
    name: 'ENCODE-ChIP',
    subjectCode: 'PROJ004',
    description: 'ChIP-seq数据集',
    size: '4.1GB',
    createTime: '2024-02-10'
  },
  {
    id: 5,
    name: 'HumanProtein',
    subjectCode: 'PROJ005',
    description: '人类蛋白质组数据',
    size: '1.5GB',
    createTime: '2024-02-15'
  },
  {
    id: 6,
    name: 'COVID-RNA',
    subjectCode: 'PROJ006',
    description: 'COVID-19 RNA测序数据',
    size: '2.8GB',
    createTime: '2024-02-20'
  },
  {
    id: 7,
    name: 'MetaGenome',
    subjectCode: 'PROJ007',
    description: '宏基因组测序数据',
    size: '5.2GB',
    createTime: '2024-03-01'
  },
  {
    id: 8,
    name: 'SingleCell-Heart',
    subjectCode: 'PROJ008',
    description: '心脏单细胞数据集',
    size: '1.9GB',
    createTime: '2024-03-05'
  }
])

// 过滤后的数据集
const filteredDatasets = computed(() => {
  let filtered = mockDatasets.value
  
  if (searchKeyword.value) {
    const keyword = searchKeyword.value.toLowerCase()
    filtered = filtered.filter(dataset => 
      dataset.name.toLowerCase().includes(keyword) ||
      dataset.subjectCode.toLowerCase().includes(keyword)
    )
  }
  
  total.value = filtered.length
  
  // 分页
  const start = (currentPage.value - 1) * pageSize.value
  const end = start + pageSize.value
  return filtered.slice(start, end)
})

// 方法
const handleSearch = () => {
  currentPage.value = 1
}

const handleSizeChange = (size: number) => {
  pageSize.value = size
  currentPage.value = 1
}

const handleCurrentChange = (page: number) => {
  currentPage.value = page
}

const isSelectable = (row: Dataset) => {
  return true
}

const handleSelectionChange = (selection: Dataset[]) => {
  tempSelectedDatasets.value = selection
}

const confirmSelection = () => {
  selectedDatasets.value = [...tempSelectedDatasets.value]
  emit('update:modelValue', selectedDatasets.value)
  dialogVisible.value = false
  ElMessage.success(`已选择 ${selectedDatasets.value.length} 个数据集`)
}

const removeDataset = (id: number) => {
  selectedDatasets.value = selectedDatasets.value.filter(dataset => dataset.id !== id)
  emit('update:modelValue', selectedDatasets.value)
}

const handleClose = () => {
  dialogVisible.value = false
  tempSelectedDatasets.value = []
}

// 监听弹窗打开，设置已选中的数据
watch(dialogVisible, async (visible) => {
  if (visible) {
    tempSelectedDatasets.value = [...selectedDatasets.value]
    await nextTick()
    // 设置表格选中状态
    if (tableRef.value) {
      selectedDatasets.value.forEach(selected => {
        const row = filteredDatasets.value.find(item => item.id === selected.id)
        if (row) {
          tableRef.value.toggleRowSelection(row, true)
        }
      })
    }
  }
})

// 监听props变化
watch(() => props.modelValue, (newValue) => {
  selectedDatasets.value = [...newValue]
}, { deep: true })
</script>

<style lang="scss" scoped>
.dataset-selector {
  .selector-header {
    display: flex;
    align-items: center;
    gap: 12px;
    margin-bottom: 16px;
    
    .selected-count {
      color: #666;
      font-size: 14px;
    }
  }
  
  .selected-datasets {
    .selected-header {
      font-size: 14px;
      color: #333;
      margin-bottom: 8px;
    }
    
    .selected-list {
      display: flex;
      flex-wrap: wrap;
      gap: 8px;
      
      .dataset-tag {
        margin: 0;
      }
    }
  }
}

.dialog-content {
  .search-bar {
    margin-bottom: 16px;
  }
  
  .dataset-table {
    margin-bottom: 16px;
  }
  
  .pagination-wrapper {
    display: flex;
    justify-content: center;
  }
}

.dialog-footer {
  display: flex;
  justify-content: flex-end;
  gap: 12px;
}
</style>
