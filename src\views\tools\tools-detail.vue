<template>
  <div class="h-full overflow-y-auto">
    <div class="mx-auto p-5">
      <div class="pb-4 border-b border-#EBEEF5 flex">
        <div>
          <a class="text-p flex gap-2 cursor-pointer items-center" @click="router.back()">
            <el-icon size="20px"><Back /></el-icon>
            返回
          </a>
        </div>

        <div class="ml-5">
          <h2 class="text-18px font-bold">BWA-MEM FASTQ Read Mapper</h2>
          <div class="text-14px text-gray-500">系统添加</div>
          <div class="mt-2">
            <el-button type="success" size="small" class="mr-2" :icon="VideoPlay">运行</el-button>
            <el-button type="info" size="small" :icon="Delete">卸载</el-button>
          </div>
        </div>
      </div>

      <div class="mt-4">
        <el-tabs>
          <el-tab-pane label="信息">
            <div class="mt-4 flex gap-8">
              <el-card shadow="never">
                <div class="info-box mb-4 p-4 rounded bg-blue-50">
                  <p>使用-MEM算法将FASTQ读取/配对或未配对映射到参考数据集</p>
                </div>

                <h3 class="text-lg font-bold mb-2">FASTQ Read Mapper</h3>

                <div class="mb-4">
                  <h4 class="font-bold">这个应用程序是做什么的?</h4>
                  <p class="mt-2">
                    这个应用程序从同一个样本中获取FASTQ读取数组(配对或未配对)。用-MEM算法将它们映射到参考基因组，并按坐标对读取进行排序。根据所选的删除选项，它还可以标记重复除并重复读取
                  </p>
                </div>

                <div class="mb-4">
                  <h4 class="font-bold">这个应用程序的典型应用案例是什么?</h4>
                  <p class="mt-2">
                    该应用可用于检测参考基因组的读数，这是大多数生物信息学分析中的典型步骤。如果您计划使用GATK执行下游变化检测，那么它可以作为第一步，或在何其他需要映射的分析。
                  </p>
                  <p class="mt-2">
                    这个应用程序使用 (Burrows-Wheeler Alignment Tool 软件包)。包括三种算法(-backtrack,SW和-
                    mem)，但本应用程序专门使用- mem算法。
                  </p>
                  <p class="mt-2">
                    -MEM算法适用于70bp～1Mbp的读取长度。与-backtrack相比，它在70-100bp的长度下读取性能更好；与其他对比工具相比，它能够更快地读取序列更快，更准确
                  </p>
                  <p class="mt-2">
                    此应用程序需要读取FASTQ文件（将个人FASTQ文件（针对单端实验）或成对FASTQ文件（针对配对实验）。阅读者时应使用Gzipped
                    FASTQ格式（x.fastq.gz或.fq.gz）。例如通常者由Illumina测序平台和其他供应商。参考是由参列索引。这必须经过gzipr的tar档案文件(x.bea-index.tar.gz)包含在
                    indexec之前输出的序列索引中
                  </p>
                </div>
              </el-card>

              <div>
                <el-card shadow="never">
                  <h3 class="text-lg font-bold mb-2">费用</h3>
                  <p>计算费用</p>
                </el-card>

                <el-card shadow="never" class="mt-4">
                  <h3 class="text-lg font-bold mb-2">应用运行</h3>
                  <p>
                    点击项目，选择你想要选择运行这个应用程序的项目。然后点击开始分析按钮，选择这个应用程序(FASTQ读取映射)
                  </p>

                  <el-divider />

                  <h3 class="text-lg font-bold mb-2">地区</h3>
                  <div class="mt-2 flex flex-wrap">
                    <el-button size="small" type="danger" class="mb-2 mr-2">China</el-button>
                    <el-button size="small" type="primary" class="mb-2 mr-2">US</el-button>
                    <el-button size="small" type="success" class="mb-2 mr-2">UK</el-button>
                    <el-button size="small" type="warning">Japan</el-button>
                  </div>

                  <el-divider />

                  <h3 class="text-lg font-bold mb-2">许可</h3>
                  <p>应用可通过互联网访问</p>

                  <el-divider />

                  <h3 class="text-lg font-bold mb-2">版本2.0.3 2019 8.30</h3>
                  <div class="update-info">
                    <h4 class="font-bold mb-2">最新活动</h4>
                    <ul class="pl-5 list-disc">
                      <li>2.0.3更新upstream</li>
                      <li>2.0更新</li>
                      <li>1.9更新</li>
                    </ul>
                  </div>
                </el-card>
              </div>
            </div>
          </el-tab-pane>
          <el-tab-pane label="版本">
            <div class="pt-4">
              <h3 class="text-lg font-bold mb-2">版本2.0.3 2019 8.30</h3>
              <div class="update-info">
                <h4 class="font-bold mb-2">最新活动</h4>
                <ul class="pl-5 list-disc">
                  <li>2.0.3更新upstream</li>
                  <li>2.0更新</li>
                  <li>1.9更新</li>
                </ul>
              </div>
            </div>
          </el-tab-pane>
        </el-tabs>
      </div>
    </div>
  </div>
</template>

<script setup lang="ts">
  import { Back, VideoPlay, Delete } from '@element-plus/icons-vue';
  import { useRouter } from 'vue-router';
  const router = useRouter();
</script>

<style lang="scss" scoped>
  :deep(.el-tabs__header) {
    margin: 0;

    .el-tabs__item {
      --el-font-size-base: 16px;
    }
  }
</style>
