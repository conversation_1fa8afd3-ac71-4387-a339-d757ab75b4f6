import { ref } from 'vue';

// 定义作业类型
export interface Job {
  id: number;
  name: string;
  state: string;
  program: string;
  user: string;
  startTime: string;
  runTime: string;
  price: string;
  url: string;
}

// 使用单例模式创建一个全局的作业服务
class JobsService {
  private static instance: JobsService;
  private jobs = ref<Job[]>([]);

  private constructor() {
    // 初始化模拟数据
    this.jobs.value = [
      {
        id: 1,
        name: 'Job_001',
        state: '运行',
        program: 'Program A',
        user: '张三',
        startTime: '2023-10-15 09:30',
        runTime: '2小时30分钟',
        price: '¥120',
        url: 'https://example.com/job1',
      },
      {
        id: 2,
        name: 'Job_002',
        state: '等待',
        program: 'Program B',
        user: '李四',
        startTime: '2023-10-15 10:15',
        runTime: '等待中',
        price: '¥80',
        url: 'https://example.com/job2',
      },
      {
        id: 3,
        name: 'Job_003',
        state: '完成',
        program: 'Program C',
        user: '王五',
        startTime: '2023-10-14 14:20',
        runTime: '5小时15分钟',
        price: '¥250',
        url: 'https://example.com/job3',
      },
      {
        id: 4,
        name: 'Job_004',
        state: '运行',
        program: 'Program D',
        user: '赵六',
        startTime: '2023-10-15 08:45',
        runTime: '3小时10分钟',
        price: '¥150',
        url: 'https://example.com/job4',
      },
      {
        id: 5,
        name: 'Job_005',
        state: '等待',
        program: 'Program E',
        user: '张三',
        startTime: '2023-10-15 11:30',
        runTime: '等待中',
        price: '¥90',
        url: 'https://example.com/job5',
      },
    ];
  }

  public static getInstance(): JobsService {
    if (!JobsService.instance) {
      JobsService.instance = new JobsService();
    }
    return JobsService.instance;
  }

  // 获取所有作业
  public getJobs(): Job[] {
    return this.jobs.value;
  }

  // 获取运行中的作业数量
  public getRunningJobsCount(): number {
    return this.jobs.value.filter((job) => job.state === '运行').length;
  }

  // 获取等待中的作业数量
  public getWaitingJobsCount(): number {
    return this.jobs.value.filter((job) => job.state === '等待').length;
  }

  // 根据过滤条件获取作业
  public getFilteredJobs(filters: any): Job[] {
    let filteredData = [...this.jobs.value];

    if (filters.name) {
      filteredData = filteredData.filter((item) => item.name.includes(filters.name));
    }

    if (filters.state) {
      filteredData = filteredData.filter((item) => item.state === filters.state);
    }

    if (filters.user) {
      filteredData = filteredData.filter((item) => item.user === filters.user);
    }

    if (filters.searchKeyword) {
      filteredData = filteredData.filter(
        (item) =>
          item.name.includes(filters.searchKeyword) ||
          item.program.includes(filters.searchKeyword) ||
          item.user.includes(filters.searchKeyword)
      );
    }

    return filteredData;
  }

  // 停止作业
  public stopJob(jobId: number): void {
    const index = this.jobs.value.findIndex((job) => job.id === jobId);
    if (index !== -1) {
      this.jobs.value[index].state = '完成';
    }
  }

  // 停止所有作业
  public stopAllJobs(): void {
    this.jobs.value.forEach((job) => {
      if (['运行', '等待'].includes(job.state)) {
        job.state = '完成';
      }
    });
  }
}

export default JobsService.getInstance();
