import { createApp } from 'vue';
import './styles/element/index.scss';
import '@unocss/reset/tailwind-compat.css';
import 'virtual:uno.css';
import './styles/index.css';
import 'dayjs/locale/zh-cn';
import ElementPlus from 'element-plus';
import App from './App.vue';
import { setupRouter } from './router/index';
import { setupStore } from './store/index';

const app = createApp(App);
setupRouter(app);
setupStore(app);

// 配置 Element Plus
app.use(ElementPlus);

app.mount('#app');
