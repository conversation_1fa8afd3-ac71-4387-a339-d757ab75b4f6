export default {
  '/rolePrivilege/assignPrivilege2': 'assignPrivilege2',
  '/rolePrivilege/assignPrivilege1': 'assignPrivilege1',
  '/rolePrivilege/findRolePrivilegeByRoleId/{roleId}': 'findRolePrivilegeByRoleIdVO',
  '/rolePrivilege/deleteRolePrivilege/{roleId}': 'deleteRolePrivilege',
  '/userRole/assignRole2': 'assignRole2',
  '/userRole/findUserRoleVOByUserIdVO/{userId}': 'findUserRoleVOByUserIdVO',
  '/userRole/deleteUserRole/{userId}': 'deleteUserRole',
  '/role/newOrUpdateEntity': 'newOrUpdateEntity_2',
  '/role/findEntityByIdCollection': 'findEntityById_2',
  '/role/deleteEntityByIdCollection': 'deleteEntityById_2',
  '/role/searchRoles/{pageNum}/{pageSize}': 'searchRoles',
  '/role/findRolesByType': 'findRolesByType',
  '/role/findEntityByIdArray/{idArray}': 'findEntityById_23',
  '/role/findEntityById/{id}': 'findEntityById_24',
  '/role/findAll/{pageNum}/{pageSize}': 'findAll_2',
  '/role/deleteEntityByIdArray/{idArray}': 'deleteEntityById_23',
  '/role/deleteEntityById/{id}': 'deleteEntityById_24',
  '/OriginalSheet/newOrUpdateEntity': 'newOrUpdateEntity_12',
  '/OriginalSheet/findEntityByIdCollection': 'findEntityById_12',
  '/OriginalSheet/deleteEntityByIdCollection': 'deleteEntityById_12',
  '/OriginalSheet/publishOriginalSheetById/{originalSheetId}': 'publishOriginalSheetById',
  '/OriginalSheet/findEntityByIdArray/{idArray}': 'findEntityById_43',
  '/OriginalSheet/findEntityById/{id}': 'findEntityById_44',
  '/OriginalSheet/findAll/{pageNum}/{pageSize}': 'findAll_12',
  '/OriginalSheet/deleteEntityByIdArray/{idArray}': 'deleteEntityById_43',
  '/OriginalSheet/deleteEntityById/{id}': 'deleteEntityById_44',
  '/OriginalSheet/clearOriginalSheetAndDatasetById/{originalSheetId}': 'clearOriginalSheetAndDatasetById',
  '/OriginalColumn/newOrUpdateEntity': 'newOrUpdateEntity_13',
  '/OriginalColumn/findEntityByIdCollection': 'findEntityById_13',
  '/OriginalColumn/deleteEntityByIdCollection': 'deleteEntityById_13',
  '/OriginalColumn/findEntityByIdArray/{idArray}': 'findEntityById_45',
  '/OriginalColumn/findEntityById/{id}': 'findEntityById_46',
  '/OriginalColumn/findAll/{pageNum}/{pageSize}': 'findAll_13',
  '/OriginalColumn/deleteEntityByIdArray/{idArray}': 'deleteEntityById_45',
  '/OriginalColumn/deleteEntityById/{id}': 'deleteEntityById_46',
  '/privilege/newOrUpdateEntity': 'newOrUpdateEntity_4',
  '/privilege/findEntityByIdCollection': 'findEntityById_4',
  '/privilege/deleteEntityByIdCollection': 'deleteEntityById_4',
  '/privilege/searchPrivileges/{pageNum}/{pageSize}': 'searchPrivileges',
  '/privilege/findPrivilegesByType': 'findPrivilegesByType',
  '/privilege/findEntityByIdArray/{idArray}': 'findEntityById_27',
  '/privilege/findEntityById/{id}': 'findEntityById_28',
  '/privilege/findAll/{pageNum}/{pageSize}': 'findAll_4',
  '/privilege/deleteEntityByIdArray/{idArray}': 'deleteEntityById_27',
  '/privilege/deleteEntityById/{id}': 'deleteEntityById_28',
  '/userEnrollment/newOrUpdateEntity': 'newOrUpdateEntity',
  '/userEnrollment/findUserEnrollmentByCriteria': 'findUserEnrollmentByCriteria',
  '/userEnrollment/findEntityByIdCollection': 'findEntityById',
  '/userEnrollment/deleteEntityByIdCollection': 'deleteEntityById',
  '/userEnrollment/findEntityByIdArray/{idArray}': 'findEntityById_19',
  '/userEnrollment/findEntityById/{id}': 'findEntityById_20',
  '/userEnrollment/findAll/{pageNum}/{pageSize}': 'findAll',
  '/userEnrollment/deleteEntityByIdArray/{idArray}': 'deleteEntityById_19',
  '/userEnrollment/deleteEntityById/{id}': 'deleteEntityById_20',
  '/user/verifyEnroll': 'verifyEnroll',
  '/user/userEnroll': 'userEnroll',
  '/user/updatePassword': 'updatePassword',
  '/user/newOrUpdateUser': 'newOrUpdateUser',
  '/user/findUserVOByCriteria': 'findUserVOByCriteria',
  '/user/userInforSecurity/{userName}': 'getUserInforSecurity',
  '/user/userInfor/{userName}': 'getUserInfor',
  '/user/unlockMDMUserById/{userId}': 'unlockMDMUserById',
  '/user/summitEnrollAgain/{userId}': 'summitEnrollAgain',
  '/user/lockMDMUserById/{userId}': 'lockMDMUserById',
  '/user/findVerifyResult/{userId}': 'findVerifyResult',
  '/user/findUserEnrollmentByUserId/{userId}/{resolutionType}/{pageNum}/{pageSize}': 'findUserEnrollmentByUserId',
  '/user/findUserByOrgId/{userId}': 'findOrgUserByUserId',
  '/user/findTeamUserRoleVOByUserId/{userId}': 'findTeamUserRoleVOByUserId',
  '/user/findPaymentByUserId/{userId}/{pageNum}/{pageSize}': 'findPaymentByUserId',
  '/user/findEnrollVerify/{userType}/{pageNum}/{pageSize}': 'findEnrollVerify',
  '/user/findEnrollProcessing/{userType}/{pageNum}/{pageSize}': 'findEnrollProcessing',
  '/user/findEnrollProcessVerify/{userType}/{pageNum}/{pageSize}': 'findEnrollProcessVerify',
  '/user/findCBDDefTableByUserId/{userId}': 'findCBDDefTableByUserId',
  '/user/findAllUserPagable/{pageNum}/{pageSize}': 'findAllUser',
  '/user/deleteMDMUserById/{userId}': 'deletePrivilegeById',
  '/annotation/findannotationByCriteria': 'findannotationByCriteria',
  '/annotation/findTotalAmountByDictionaryCodeOrg': 'findTotalAmountByDictionaryCodeOrg',
  '/annotation/findTotalAmountByDictionaryCodeDisease': 'findTotalAmountByDictionaryCodeDisease',
  '/annotation/findTotalAmountByAnnotationId/{dictionaryCode}': 'findTotalAmountBySystemDictionaryCode',
  '/order/verifyOrderById/{orderId}': 'verifyOrderById',
  '/order/newOrUpdateOrder': 'newOrUpdateOrder',
  '/order/newOrUpdateOrderItems': 'newOrUpdateOrderItem',
  '/order/newOrUpdateOrderItem': 'newOrUpdateOrderItem_1',
  '/order/findProductVOByCriteria': 'findOrderVOByCriteria',
  '/order/findAllOrderItemById': 'findAllOrderItemById',
  '/order/deleteOrderItemById': 'deleteOrderItemById',
  '/order/submitOrder/{orderId}': 'submitOrder',
  '/order/findUserOrderProcessingByUserId/{userId}/{pageNum}/{pageSize}': 'findUserOrderProcessingByUserId',
  '/order/findUserOrderProcessingByOrderId/{orderId}/{pageNum}/{pageSize}': 'findUserOrderProcessingByOrderId',
  '/order/findOrderItemVOByOrderId/{orderId}': 'findOrderItemVOByOrderId',
  '/order/findOrderByUserId/{userId}/{pageNum}/{pageSize}': 'findOrderByUserId',
  '/order/findOrderById/{orderId}': 'findOrderById',
  '/order/findInvoiceVOByOrderId/{orderId}': 'findInvoiceVOByOrderId',
  '/order/findAllOrderProcessingById/{orderProcessingId}': 'findAllOrderProcessingById',
  '/order/findAllOrder/{pageNum}/{pageSize}': 'findAllOrder',
  '/order/deleteOrderById/{orderId}': 'deleteOrderById',
  '/invoice/newOrUpdateProduct': 'newOrUpdateOrg_1',
  '/invoice/findInvoiceByCriteria': 'findInvoiceByCriteria',
  '/invoice/findAllInvoiceById/{processId}': 'findAllInvoiceById',
  '/invoice/findAllInvoice/{pageNum}/{pageSize}': 'findAllInvoice',
  '/invoice/deleteProductById/{productId}': 'deleteProductById',
  '/process/newOrUpdateProcess': 'newOrUpdateProcess',
  '/process/findProcessByCriteria': 'findProcessByCriteria',
  '/process/findAllProcessById/{processId}': 'findAllProcessById',
  '/process/findAllProcess/{pageNum}/{pageSize}': 'findAllProcess',
  '/process/deleteProcessById/{processId}': 'deleteProcessById',
  '/org/removeUserFromOrg': 'deleteOrgUserById',
  '/org/newOrUpdateOrg': 'newOrUpdateOrg',
  '/org/findOrgVOByCriteria': 'findOrgVOByCriteria_1',
  '/org/addUserToOrg': 'newOrUpdateOrgUser',
  '/org/findUserByOrgId/{orgId}': 'findUserByOrgId',
  '/org/findTeamByOrgId/{orgId}': 'findTeamByOrgId',
  '/org/findOrgById/{orgId}': 'findOrgById',
  '/org/findAllOrgById/{orgId}': 'findAllOrgById',
  '/org/findAllOrg/{pageNum}/{pageSize}': 'findAllOrg',
  '/org/deleteOrgById/{orgId}': 'deleteOrgById',
  '/Directory/newOrUpdateEntity': 'newOrUpdateEntity_15',
  '/Directory/findEntityByIdCollection': 'findEntityById_15',
  '/Directory/deleteEntityByIdCollection': 'deleteEntityById_15',
  '/Directory/getTopDirectory': 'getTopDirectory',
  '/Directory/findEntityByIdArray/{idArray}': 'findEntityById_49',
  '/Directory/findEntityById/{id}': 'findEntityById_50',
  '/Directory/findAll/{pageNum}/{pageSize}': 'findAll_15',
  '/Directory/deleteEntityByIdArray/{idArray}': 'deleteEntityById_49',
  '/Directory/deleteEntityById/{id}': 'deleteEntityById_50',
  '/dataSource/testDataSource': 'testDataSource',
  '/catalogue/unmountMedicalFieldFromCatalogue': 'unmountMedicalFieldFromCatalogue',
  '/catalogue/newOrUpdateCatalogueVo': 'newOrUpdateCatalogue',
  '/catalogue/mountMedicalFieldToCatalogue': 'mountMedicalFieldToCatalogue',
  '/catalogue/{id}': 'getCatalogue',
  '/catalogue/topBasicCatalogues': 'getTopBasicCatalogues',
  '/catalogue/medicalFieldInCatalogue/{id}': 'getMedicalFieldInCatalogue',
  '/catalogue/getParentCatalogue/{id}': 'getParentCatalogue',
  '/catalogue/getChildCatalogue/{id}': 'getChildCatalogue',
  '/catalogue/getCatalogueByType': 'getCatalogueByType',
  '/catalogue/getCatalogueByTitle/{title}': 'getCatalogueByTitle',
  '/catalogue/getBrotherCatalogue/{id}': 'getBrotherCatalogue',
  '/catalogue/deleteCatalogue/{id}/{isObligated}': 'deleteCatalogue',
  '/catalogue/dataBrowse/chart': 'dataBrowse',
  '/dataSource/newOrUpdateDatabase': 'newOrUpdateDatabaseVO',
  '/dataSource/addTable': 'addTable',
  '/dataSource/addField': 'addField',
  '/dataSource/synchronizeVolume/{dbId}': 'synchronizeVolume',
  '/dataSource/findDBVOByDbId/{dbId}': 'findDBVOByDbId',
  '/dataSource/findAllTableVOByDbId/{dbId}': 'findAllTableVOByDbId',
  '/dataSource/findAllFieldVOByDbIdTblId/{dbId}/{tblId}': 'findAllFieldVOByDbIdTblId',
  '/dataSource/findAllDb': 'findAllDb',
  '/dataSource/deleteTableById/{id}/{isObligated}': 'deleteTableById',
  '/dataSource/deleteFieldById/{id}': 'deleteFieldById',
  '/dataSource/deleteDatabaseById/{id}/{isObligated}': 'deleteDatabaseById',
  '/dataSource/checkConnectionDBVOByDbId/{dbId}': 'checkConnectionDBVOByDbId',
  '/medicalData/uploadOriginalDocument/{orginalDocumentId}': 'uploadDocument',
  '/medicalData/newOrUpdateMedicalFieldVO': 'newOrUpdateMedicalFieldVO',
  '/medicalData/newOrUpdateAlternative/{mfId}': 'newOrUpdateAlternative',
  '/medicalData/findMedicalFieldsByFileInforIdAndDynamicConditions/{fileInforId}':
    'findMedicalFieldsByFileInforIdAndDynamicConditions',
  '/medicalData/findMedicalFieldVOByCriteria': 'findMedicalFieldVOByCriteria',
  '/medicalData/addStatisticTxtMedicalFieldId/{medicalFieldId}': 'addStatisticTxtMedicalFieldId',
  '/medicalData/addStatisticLongIntMedicalFieldId/{medicalFieldId}': 'addStatisticLongIntMedicalFieldId',
  '/medicalData/addStatisticFloatMedicalFieldId/{medicalFieldId}': 'addStatisticFloatMedicalFieldId',
  '/medicalData/addStatisticDateTimeMedicalFieldId/{medicalFieldId}': 'addStatisticDateTimeMedicalFieldId',
  '/medicalData/addStatisticCategoricalMedicalFieldId/{medicalFieldId}': 'addStatisticCategoricalMedicalFieldId',
  '/medicalData/addPublication/{mfId}': 'addPublication',
  '/medicalData/addOriginalDocument/{mfId}': 'addOriginalDocument',
  '/medicalData/{id}': 'getMedicalField',
  '/medicalData/synchronizeRecordCount/{medicalFieldId}': 'synchronizeRecordCount',
  '/medicalData/removeDocument/{orginalDocumentId}': 'removeDocument',
  '/medicalData/getProbabilityDistribution/{medicalFieldId}': 'getProbabilityDistribution',
  '/medicalData/findStatisticByMedicalFieldId/{medicalFieldId}': 'findStatisticByMedicalFieldId',
  '/medicalData/findPublicationByMedicalFieldId/{mfId}': 'findPublicationByMedicalFieldId',
  '/medicalData/findPlatStatistic': 'findPlatStatistic',
  '/medicalData/findOriginalDocumentByMedicalFieldId/{mfId}': 'findOriginalDocumentByMedicalFieldId',
  '/medicalData/findMedicalFieldVOByValueType/{mdfId}': 'findDataSourceById',
  '/medicalData/findFileInforByMedicalFieldId/{medicalFieldId}': 'findFileInforByMedicalFieldId',
  '/medicalData/findCatalogueVoBymdfId/{mdfId}': 'findCatalogueVoBymdfId',
  '/medicalData/findCBDDefFieldByMfId/{mfId}': 'findCBDDefFieldByMfId',
  '/medicalData/findAlternativeByMedicalFieldId/{mfId}': 'findAlternativeByMedicalFieldId',
  '/medicalData/disassociateCBDField/{mfId}/{cfdFieldId}': 'disassociateCBDField',
  '/medicalData/detail/{id}': 'getMedicalFieldDetail',
  '/medicalData/deleteStatistic/{statisticId}': 'deleteStatistic',
  '/medicalData/deletePublication/{mfId}/{pubId}': 'deletePublication',
  '/medicalData/deleteOriginalDocument/{mfId}/{ordId}': 'deleteOriginalDocument',
  '/medicalData/deleteMedicalFieldById/{mfId}': 'deleteMedicalFieldById',
  '/medicalData/deleteAlternative/{mfId}/{altId}': 'deleteAlternative',
  '/medicalData/associateCBDField/{mfId}/{cfdFieldId}': 'associateCBDField',
  '/team/removeUserFromTeam': 'deleteTeamUserById',
  '/team/removeTeamFromOrg/{orgId}/{teamId}': 'removeTeamFromOrg',
  '/team/newOrUpdateTeam': 'newOrUpdateTeam',
  '/team/findOrgVOByCriteria': 'findOrgVOByCriteria',
  '/team/addUserToTeam': 'newOrUpdateTeamUser',
  '/team/addTeamToOrg/{orgId}': 'addTeamToOrg',
  '/team/addTeamToOrg/{orgId}/{teamId}': 'addTeamToOrg_1',
  '/team/findUserByIdNotIn': 'findUserByIdNotIn',
  '/team/findTeamUserRoleVOByTeamId/{teamId}': 'findTeamUserRoleVOByTeamId',
  '/team/findTeamById/{teamId}': 'findTeamById',
  '/team/findApplicationTeamRoleVOByTeamId/{teamId}': 'findApplicationTeamRoleVOByTeamId',
  '/team/findAllTeam': 'findAllTeam',
  '/team/findAllTeamById': 'findAllTeamById',
  '/team/findAllApplicationByTeamId/{teamId}': 'findAllApplicationByTeamId',
  '/team/deleteTeamById/{teamId}': 'deleteTeamById',
  '/CommonFile/uploadFile': 'uploadFile_12',
  '/CommonFile/uploadFile/{fileId}': 'uploadFile_13',
  '/CommonFile/newOrUpdateEntity': 'newOrUpdateEntity_18',
  '/CommonFile/findEntityByIdCollection': 'findEntityById_18',
  '/CommonFile/deleteEntityByIdCollection': 'deleteEntityById_18',
  '/CommonFile/findEntityByIdArray/{idArray}': 'findEntityById_55',
  '/CommonFile/findEntityById/{id}': 'findEntityById_56',
  '/CommonFile/findAll/{pageNum}/{pageSize}': 'findAll_18',
  '/CommonFile/deleteEntityByIdArray/{idArray}': 'deleteEntityById_55',
  '/CommonFile/deleteEntityById/{id}': 'deleteEntityById_56',
  '/applicationProcessing/newOrUpdateEntity': 'newOrUpdateEntity_11',
  '/applicationProcessing/findEntityByIdCollection': 'findEntityById_11',
  '/applicationProcessing/deleteEntityByIdCollection': 'deleteEntityById_11',
  '/applicationProcessing/findEntityByIdArray/{idArray}': 'findEntityById_41',
  '/applicationProcessing/findEntityById/{id}': 'findEntityById_42',
  '/applicationProcessing/findAll/{pageNum}/{pageSize}': 'findAll_11',
  '/applicationProcessing/deleteEntityByIdArray/{idArray}': 'deleteEntityById_41',
  '/applicationProcessing/deleteEntityById/{id}': 'deleteEntityById_42',
  '/sourceCode/uploadFile': 'uploadFile',
  '/sourceCode/uploadFile/{sourceCodeId}': 'uploadFile_1',
  '/sourceCode/newOrUpdateEntity': 'newOrUpdateEntity_1',
  '/sourceCode/findEntityByIdCollection': 'findEntityById_1',
  '/sourceCode/deleteEntityByIdCollection': 'deleteEntityById_1',
  '/publicationreport/uploadFile': 'uploadFile_2',
  '/publicationreport/uploadFile/{publicationReportId}': 'uploadFile_3',
  '/publicationreport/newOrUpdateEntity': 'newOrUpdateEntity_3',
  '/publicationreport/findEntityByIdCollection': 'findEntityById_3',
  '/publicationreport/deleteEntityByIdCollection': 'deleteEntityById_3',
  '/manuscript/uploadFile': 'uploadFile_4',
  '/manuscript/uploadFile/{manuscriptId}': 'uploadFile_5',
  '/manuscript/newOrUpdateEntity': 'newOrUpdateEntity_6',
  '/manuscript/findEntityByIdCollection': 'findEntityById_6',
  '/manuscript/deleteEntityByIdCollection': 'deleteEntityById_6',
  '/datasetFile/uploadFile': 'uploadFile_6',
  '/datasetFile/uploadFile/{datasetFileId}': 'uploadFile_7',
  '/datasetFile/newOrUpdateEntity': 'newOrUpdateEntity_8',
  '/datasetFile/findEntityByIdCollection': 'findEntityById_8',
  '/datasetFile/deleteEntityByIdCollection': 'deleteEntityById_8',
  '/attachment/uploadFile': 'uploadFile_8',
  '/attachment/uploadFile/{attachmentId}': 'uploadFile_9',
  '/attachment/newOrUpdateEntity': 'newOrUpdateEntity_10',
  '/attachment/findEntityByIdCollection': 'findEntityById_10',
  '/attachment/deleteEntityByIdCollection': 'deleteEntityById_10',
  '/sourceCode/findEntityByIdArray/{idArray}': 'findEntityById_21',
  '/sourceCode/findEntityById/{id}': 'findEntityById_22',
  '/sourceCode/findAll/{pageNum}/{pageSize}': 'findAll_1',
  '/sourceCode/deleteEntityByIdArray/{idArray}': 'deleteEntityById_21',
  '/sourceCode/deleteEntityById/{id}': 'deleteEntityById_22',
  '/publicationreport/findEntityByIdArray/{idArray}': 'findEntityById_25',
  '/publicationreport/findEntityById/{id}': 'findEntityById_26',
  '/publicationreport/findAll/{pageNum}/{pageSize}': 'findAll_3',
  '/publicationreport/deleteEntityByIdArray/{idArray}': 'deleteEntityById_25',
  '/publicationreport/deleteEntityById/{id}': 'deleteEntityById_26',
  '/manuscript/findEntityByIdArray/{idArray}': 'findEntityById_31',
  '/manuscript/findEntityById/{id}': 'findEntityById_32',
  '/manuscript/findAll/{pageNum}/{pageSize}': 'findAll_6',
  '/manuscript/deleteEntityByIdArray/{idArray}': 'deleteEntityById_31',
  '/manuscript/deleteEntityById/{id}': 'deleteEntityById_32',
  '/datasetFile/findEntityByIdArray/{idArray}': 'findEntityById_35',
  '/datasetFile/findEntityById/{id}': 'findEntityById_36',
  '/datasetFile/findAll/{pageNum}/{pageSize}': 'findAll_8',
  '/datasetFile/deleteEntityByIdArray/{idArray}': 'deleteEntityById_35',
  '/datasetFile/deleteEntityById/{id}': 'deleteEntityById_36',
  '/attachment/findEntityByIdArray/{idArray}': 'findEntityById_39',
  '/attachment/findEntityById/{id}': 'findEntityById_40',
  '/attachment/findAll/{pageNum}/{pageSize}': 'findAll_10',
  '/attachment/deleteEntityByIdArray/{idArray}': 'deleteEntityById_39',
  '/attachment/deleteEntityById/{id}': 'deleteEntityById_40',
  '/application/verifyApplication': 'verifyApplication',
  '/application/syncCBDDefTablesForApplication/{applicationId}': 'syncCBDDefTablesForApplication',
  '/application/removeTeamRoleFromApplication': 'deleteApplicationTeaRolemById',
  '/application/removeTeamFromApplication': 'deleteApplicationTeamById',
  '/application/newOrUpdateApplication': 'newOrUpdateApplication',
  '/application/findApplicationVOByCriteria': 'findApplicationVOByCriteria',
  '/application/addTeamToApllication': 'newOrUpdateApplicationTeam',
  '/application/addTeamRoleToApplication': 'newOrUpdateApplicationTeamRole',
  '/application/findVerifyResult/{applicationId}': 'findVerifyResult_1',
  '/application/findTableByApplicationId/{applicationId}/{pageNum}/{pageSize}': 'findTableByApplicationId',
  '/application/findOrderByApplicationId/{applicationId}/{pageNum}/{pageSize}': 'findOrderByApplicationId',
  '/application/findApplicationTeamRoleByAppId/{appId}': 'findApplicationTeamRoleByAppId',
  '/application/findApplicationTeamByAppId/{appId}': 'findApplicationTeamByAppId',
  '/application/findAppById/{appId}': 'findAppById',
  '/application/findAllVerifyApplicationByStateIn/{pageNum}/{pageSize}': 'findAllVerifyApplicationByStateIn',
  '/application/findAllVerifyApplication/{pageNum}/{pageSize}': 'findAllVerifyApplication',
  '/application/findAllApplicationById/{appId}': 'findAllApplicationById',
  '/application/findAllApplication/{pageNum}/{pageSize}': 'findAllApplication',
  '/application/deleteApplicationById/{applicationId}': 'deleteApplicationById',
  '/application/applyApplication/{applicationId}/{userId}': 'applyApplication',
  '/enrollment/newOrUpdateEntity': 'newOrUpdateEntity_7',
  '/enrollment/findEntityByIdCollection': 'findEntityById_7',
  '/enrollment/findEnrollmentByCriteria': 'findEnrollmentByCriteria',
  '/enrollment/deleteEntityByIdCollection': 'deleteEntityById_7',
  '/enrollment/findUserEnrollmentByUserId/{enrollmentId}/{resolutionType}/{pageNum}/{pageSize}':
    'findUserEnrollmentByUserId_1',
  '/enrollment/findUserEnrollment/{enrollmentId}/{pageNum}/{pageSize}': 'findUserEnrollmentByUserId_2',
  '/enrollment/findEntityByIdArray/{idArray}': 'findEntityById_33',
  '/enrollment/findEntityById/{id}': 'findEntityById_34',
  '/enrollment/findAll/{pageNum}/{pageSize}': 'findAll_7',
  '/enrollment/deleteEntityByIdArray/{idArray}': 'deleteEntityById_33',
  '/enrollment/deleteEntityById/{id}': 'deleteEntityById_34',
  '/FileInfor/uploadFileId/{fileInforId}': 'uploadFile_10',
  '/FileInfor/uploadFileId/hasmdd/{userIdCur}/{cbdDatabaseId}': 'uploadFile_11',
  '/FileInfor/setFileInforAnnotation/{annotationId}': 'setFileInforAnnotation',
  '/FileInfor/newOrUpdateEntity': 'newOrUpdateEntity_14',
  '/FileInfor/getDataInventory': 'getDataInventory',
  '/FileInfor/findFileInforByUserId/{userId}/{pageNum}/{pageSize}': 'findFileInforByUserId',
  '/FileInfor/findFileInforByCriteria': 'findFileInforByCriteria',
  '/FileInfor/findFileInforByAnnotationId': 'findFileInforByAnnotationId',
  '/FileInfor/findEntityByIdCollection': 'findEntityById_14',
  '/FileInfor/exportMultiMedicalDataSetToDatabase/{fileId}': 'exportMultiMedicalDataSetToDatabase',
  '/FileInfor/exportMedicalDataSetToDatabase/{fileId}': 'exportMedicalDataSetToDatabase',
  '/FileInfor/deleteEntityByIdCollection': 'deleteEntityById_14',
  '/FileInfor/status': 'getThreadPoolStatus',
  '/FileInfor/setDatabase/{fileInforId}/{databaseId}': 'setDatabase',
  '/FileInfor/findMedicalFieldsByTableId': 'findMedicalFieldsByTableId',
  '/FileInfor/findHistoryFileByFileInforId/{fileInforId}': 'findHistoryFileByFileInforId',
  '/FileInfor/findEntityByIdArray/{idArray}': 'findEntityById_47',
  '/FileInfor/findEntityById/{id}': 'findEntityById_48',
  '/FileInfor/findAll/{pageNum}/{pageSize}': 'findAll_14',
  '/FileInfor/deleteEntityByIdArray/{idArray}': 'deleteEntityById_47',
  '/FileInfor/deleteEntityById/{id}': 'deleteEntityById_48',
  '/Dictionary/newOrUpdateEntity': 'newOrUpdateEntity_17',
  '/Dictionary/findEntityByIdCollection': 'findEntityById_17',
  '/Dictionary/deleteEntityByIdCollection': 'deleteEntityById_17',
  '/Dictionary/findEntityByIdArray/{idArray}': 'findEntityById_53',
  '/Dictionary/findEntityById/{id}': 'findEntityById_54',
  '/Dictionary/findByState/{pageNum}/{pageSize}': 'findByState_1',
  '/Dictionary/findAll/{pageNum}/{pageSize}': 'findAll_17',
  '/Dictionary/deleteEntityByIdArray/{idArray}': 'deleteEntityById_53',
  '/Dictionary/deleteEntityById/{id}': 'deleteEntityById_54',
  '/DictionaryValue/newOrUpdateEntity': 'newOrUpdateEntity_16',
  '/DictionaryValue/findEntityByIdCollection': 'findEntityById_16',
  '/DictionaryValue/deleteEntityByIdCollection': 'deleteEntityById_16',
  '/DictionaryValue/findEntityByIdArray/{idArray}': 'findEntityById_51',
  '/DictionaryValue/findEntityById/{id}': 'findEntityById_52',
  '/DictionaryValue/findByBictionaryCode/{pageNum}/{pageSize}': 'findByBictionaryCode',
  '/DictionaryValue/findAll/{pageNum}/{pageSize}': 'findAll_16',
  '/DictionaryValue/deleteEntityByIdArray/{idArray}': 'deleteEntityById_51',
  '/DictionaryValue/deleteEntityById/{id}': 'deleteEntityById_52',
  '/payment/newOrUpdateEntity': 'newOrUpdateEntity_5',
  '/payment/findPaymentByCriteria': 'findPaymentByCriteria',
  '/payment/findEntityByIdCollection': 'findEntityById_5',
  '/payment/deleteEntityByIdCollection': 'deleteEntityById_5',
  '/payment/generateQRCode': 'generateQRCode',
  '/payment/findUserByByPaymentId/{paymentId}': 'findUserByByPaymentId',
  '/payment/findPaymentByOrderId/{orderId}': 'findPaymentByOrderId',
  '/payment/findOrderByPaymentId/{paymentId}': 'findOrderByPaymentId',
  '/payment/findEntityByIdArray/{idArray}': 'findEntityById_29',
  '/payment/findEntityById/{id}': 'findEntityById_30',
  '/payment/findAll/{pageNum}/{pageSize}': 'findAll_5',
  '/payment/deleteEntityByIdArray/{idArray}': 'deleteEntityById_29',
  '/payment/deleteEntityById/{id}': 'deleteEntityById_30',
  '/attachmentMaterial/uploadAttachmentMaterial': 'uploadAttachmentMaterial',
  '/attachmentMaterial/uploadAttachmentMaterialId/{attachmentMaterialId}': 'uploadAttachmentMaterial_1',
  '/attachmentMaterial/newOrUpdateEntity': 'newOrUpdateEntity_9',
  '/attachmentMaterial/findEntityByIdCollection': 'findEntityById_9',
  '/attachmentMaterial/findAttachmentMaterialByCriteria': 'findAttachmentMaterialByCriteria',
  '/attachmentMaterial/deleteEntityByIdCollection': 'deleteEntityById_9',
  '/attachmentMaterial/removeAttachmentMaterial/{attachmentMaterialId}': 'removeAttachmentMaterial',
  '/attachmentMaterial/getAttachmentMaterialfile/{attachmentMaterialId}': 'getAttachmentMaterialfile',
  '/attachmentMaterial/findEntityByIdArray/{idArray}': 'findEntityById_37',
  '/attachmentMaterial/findEntityById/{id}': 'findEntityById_38',
  '/attachmentMaterial/findAll/{pageNum}/{pageSize}': 'findAll_9',
  '/attachmentMaterial/deleteEntityByIdArray/{idArray}': 'deleteEntityById_37',
  '/attachmentMaterial/deleteEntityById/{id}': 'deleteEntityById_38',
  '/teamUserRole/deleteTeamUserRole/{userId}': 'deleteTeamUserRole',
  '/teamUserRole/assignTeamUserRoles': 'assignTeamUserRoles',
  '/teamUserRole/findTeamUserRoleByTeamUserId': 'findTeamUserRoleByTeamUserId',
  '/api/tools/{id}': 'deleteTool',
  '/api/tools/{id}/online': 'onlineTool',
  '/api/tools/{id}/offline': 'offlineTool',
  '/api/tools/{id}/maintenance': 'maintenanceTool',
};
