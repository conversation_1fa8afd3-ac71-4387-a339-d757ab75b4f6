import axios from 'axios';
import type { AxiosError, AxiosRequestConfig, AxiosResponse, CreateAxiosDefaults } from 'axios';
import { ElMessage, ElMessageBox } from 'element-plus';
import { router } from '@/router/index';
import { useUsers } from '@/store/user-info';

const defaultOptions: CreateAxiosDefaults = {
  baseURL: import.meta.env.VITE_API_URL,
  timeout: 5 * 60 * 1000,
  headers: {
    'Content-Type': 'application/json',
  },
};
const instance = axios.create(defaultOptions);

//请求拦截
instance.interceptors.request.use(
  (config) => {
    const token = useUsers().user.token;
    if (token && !config.headers?.skipToken) {
      config.headers.Authorization = 'Bearer ' + token;
    }
    return config;
  },
  (error: AxiosError) => {
    return Promise.reject(error);
  }
);

function reLogin() {
  console.log('reLogin');
  // 清除token
  localStorage.clear();
  // 重置状态
  isRefreshing = false;
  useUsers().quit();
  // 跳转到登录页
  router.push({ name: 'Login' });
}

// 变量isRefreshing
let isRefreshing = false;
// 后续的请求队列
type TokenResolver = (newToken: string) => void;
let requestList: TokenResolver[] = [];
// 响应拦截
instance.interceptors.response.use(
  (response) => {
    const data = response.data;
    if (response.config.responseType === 'blob') {
      return downloadFunc(response);
    }

    if (Array.isArray(data)) {
      const msgs = <any[]>[];
      data.forEach((item) => {
        if (item.code != 0) {
          msgs.push(item.msg);
        }
      });
      if (msgs.length) {
        ElMessage.error(msgs.join('；'));
        return Promise.reject(data);
      } else {
        return data;
      }
    }

    const { code, msg } = data;
    if (code === undefined) {
      //token请求返回的格式没有code
      return data;
    } else if (code === 0) {
      return data;
    } else {
      ElMessage.error(msg);
      return Promise.reject(msg);
    }
  },

  async (error: AxiosError<R, any>) => {
    const response = error.response!;
    const status = error.response!.status;
    const data = error.response!.data;
    if (status === 424) {
      if (isRefreshing) {
        // 后面的请求走这里排队
        // 返回未执行 resolve 的 Promise
        return new Promise((resolve) => {
          // 用函数形式将 resolve 存入，等待获取新token后再执行
          requestList.push((newToken: string) => {
            response.config.headers.token = newToken;
            resolve(instance(response.config));
          });
        });
      }

      if (!useUsers().user.refreshToken) {
        reLogin();
        return;
      }

      try {
        isRefreshing = true;
        const res: any = await useUsers().refreshToken();
        if (res && res.access_token) {
          // 替换新accessToken
          error.response!.config.headers.token = res.access_token;

          // token 刷新后将数组里的请求队列方法重新执行
          requestList.forEach((cb: TokenResolver) => cb(res.access_token));
          // 重新请求完清空
          requestList = [];

          // 继续未完成的请求
          const resp = await instance(error.response!.config);
          // 重置状态
          isRefreshing = false;
          return resp;
        } else {
          reLogin();
        }
      } catch (error) {
        reLogin();
      }
    } else if (data.msg) {
      ElMessage.error(data.msg);
    } else {
      ElMessage.error('内部服务器错误');
    }
    return Promise.reject(data);
  }
);

function downloadFunc(response: AxiosResponse<any, any>) {
  const blob = new Blob([response.data], { type: response.headers['content-type'] });
  // 创建新的URL并指向File对象或者Blob对象的地址
  const blobURL = window.URL.createObjectURL(blob);
  // 创建a标签，用于跳转至下载链接
  const tempLink = document.createElement('a');
  tempLink.style.display = 'none';
  tempLink.href = blobURL;
  const contentDisposition = response.headers['content-disposition'] || 'attachment;filename=Download';
  tempLink.setAttribute('download', decodeURI(contentDisposition.split(';')[1].split('=')[1]));
  // 兼容：某些浏览器不支持HTML5的download属性
  if (typeof tempLink.download === 'undefined') {
    tempLink.setAttribute('target', '_blank');
  }
  // 挂载a标签
  document.body.appendChild(tempLink);
  tempLink.click();
  document.body.removeChild(tempLink);
  // 释放blob URL地址
  window.URL.revokeObjectURL(blobURL);
  return { code: 0 };
}

export function request<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
  return instance(url, config);
}

export function upload<T>(url: string, config: AxiosRequestConfig): Promise<T> {
  if (config.headers) {
    config.headers['Content-Type'] = 'multipart/form-data';
  } else {
    config.headers = { 'Content-Type': 'multipart/form-data' };
  }
  return instance(url, config);
}

export function download<T>(url: string, config?: AxiosRequestConfig): Promise<T> {
  if (config) {
    config.responseType = 'blob';
    config.timeout = 24 * 60 * 60 * 1000;
  } else {
    config = {
      responseType: 'blob',
    };
  }
  return instance(url, config);
}

export default instance;
